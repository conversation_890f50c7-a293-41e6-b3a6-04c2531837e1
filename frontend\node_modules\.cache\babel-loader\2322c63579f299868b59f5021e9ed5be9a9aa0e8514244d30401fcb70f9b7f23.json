{"ast": null, "code": "var _jsxFileName = \"D:\\\\study\\\\college\\\\junior2\\\\dataBase\\\\system2\\\\frontend\\\\src\\\\pages\\\\Analytics.js\",\n  _s = $RefreshSig$();\n/**\n * 数据分析页面\n * 展示健康数据的深度分析和可视化图表\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Select, DatePicker, Tabs, Statistic, Progress, Alert } from 'antd';\n// 暂时不使用图表库，使用简单的数据展示\nimport { RiseOutlined, FallOutlined, HeartOutlined, TrophyOutlined, SmileOutlined } from '@ant-design/icons';\nimport styled from 'styled-components';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  TabPane\n} = Tabs;\n\n// 样式化组件\nconst PageTitle = styled.h1`\n  color: white;\n  font-size: 28px;\n  font-weight: 700;\n  margin-bottom: 24px;\n  text-align: center;\n`;\n_c = PageTitle;\nconst StyledCard = styled(Card)`\n  background: rgba(255, 255, 255, 0.25) !important;\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.18) !important;\n  border-radius: 20px !important;\n  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37) !important;\n  transition: all 0.3s ease !important;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5) !important;\n  }\n  \n  .ant-card-head {\n    background: transparent !important;\n    border-bottom: 1px solid rgba(255, 255, 255, 0.18) !important;\n    \n    .ant-card-head-title {\n      color: white !important;\n      font-weight: 600 !important;\n    }\n  }\n  \n  .ant-card-body {\n    background: transparent !important;\n  }\n`;\n_c2 = StyledCard;\nconst ControlPanel = styled.div`\n  background: rgba(255, 255, 255, 0.15);\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.18);\n  border-radius: 16px;\n  padding: 20px;\n  margin-bottom: 24px;\n  display: flex;\n  gap: 16px;\n  align-items: center;\n  flex-wrap: wrap;\n  \n  .control-item {\n    display: flex;\n    flex-direction: column;\n    gap: 8px;\n    \n    label {\n      color: rgba(255, 255, 255, 0.8);\n      font-size: 14px;\n      font-weight: 500;\n    }\n  }\n`;\n_c3 = ControlPanel;\nconst StyledTabs = styled(Tabs)`\n  .ant-tabs-nav {\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 12px;\n    padding: 4px;\n    margin-bottom: 24px;\n    \n    .ant-tabs-tab {\n      background: transparent !important;\n      border: none !important;\n      color: rgba(255, 255, 255, 0.7) !important;\n      border-radius: 8px !important;\n      margin: 0 4px !important;\n      \n      &.ant-tabs-tab-active {\n        background: rgba(255, 255, 255, 0.2) !important;\n        color: white !important;\n      }\n    }\n    \n    .ant-tabs-ink-bar {\n      display: none !important;\n    }\n  }\n`;\n_c4 = StyledTabs;\nconst MetricCard = styled(StyledCard)`\n  text-align: center;\n  \n  .ant-statistic-title {\n    color: rgba(255, 255, 255, 0.8) !important;\n    font-size: 14px !important;\n    margin-bottom: 8px !important;\n  }\n  \n  .ant-statistic-content {\n    color: white !important;\n    \n    .ant-statistic-content-value {\n      font-weight: 700 !important;\n    }\n  }\n  \n  .trend-indicator {\n    margin-top: 8px;\n    font-size: 12px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 4px;\n    \n    &.positive {\n      color: #34C759;\n    }\n    \n    &.negative {\n      color: #FF3B30;\n    }\n    \n    &.neutral {\n      color: rgba(255, 255, 255, 0.6);\n    }\n  }\n`;\n\n// 图表颜色配置\n_c5 = MetricCard;\nconst COLORS = ['#007AFF', '#34C759', '#FF9500', '#FF3B30', '#AF52DE', '#5AC8FA'];\nfunction Analytics() {\n  _s();\n  const [selectedMetric, setSelectedMetric] = useState('health_score');\n  const [dateRange, setDateRange] = useState([dayjs().subtract(30, 'day'), dayjs()]);\n  const [analyticsData, setAnalyticsData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // 模拟分析数据\n  const mockAnalyticsData = {\n    trends: {\n      health_score: {\n        labels: ['1月1日', '1月8日', '1月15日', '1月22日', '1月29日'],\n        datasets: [{\n          label: '健康评分',\n          data: [75, 78, 82, 80, 85],\n          borderColor: '#007AFF',\n          backgroundColor: 'rgba(0, 122, 255, 0.1)',\n          fill: true,\n          tension: 0.4\n        }]\n      },\n      weight: {\n        labels: ['1月1日', '1月8日', '1月15日', '1月22日', '1月29日'],\n        datasets: [{\n          label: '体重 (kg)',\n          data: [70.5, 70.2, 69.8, 69.5, 69.2],\n          borderColor: '#34C759',\n          backgroundColor: 'rgba(52, 199, 89, 0.1)',\n          fill: true,\n          tension: 0.4\n        }]\n      },\n      heart_rate: {\n        labels: ['1月1日', '1月8日', '1月15日', '1月22日', '1月29日'],\n        datasets: [{\n          label: '心率 (bpm)',\n          data: [75, 73, 72, 71, 70],\n          borderColor: '#FF3B30',\n          backgroundColor: 'rgba(255, 59, 48, 0.1)',\n          fill: true,\n          tension: 0.4\n        }]\n      }\n    },\n    exerciseStats: {\n      labels: ['跑步', '游泳', '健身', '瑜伽'],\n      datasets: [{\n        data: [12, 8, 15, 6],\n        backgroundColor: ['#007AFF', '#34C759', '#FF9500', '#AF52DE'],\n        borderWidth: 0\n      }]\n    },\n    moodDistribution: {\n      labels: ['很好', '好', '一般', '不好'],\n      datasets: [{\n        data: [8, 12, 6, 2],\n        backgroundColor: ['#34C759', '#007AFF', '#FF9500', '#FF3B30'],\n        borderWidth: 0\n      }]\n    },\n    weeklyActivity: {\n      labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],\n      datasets: [{\n        label: '运动时长(分钟)',\n        data: [45, 30, 60, 0, 40, 90, 75],\n        backgroundColor: '#007AFF'\n      }, {\n        label: '睡眠时长(小时)',\n        data: [7.5, 8, 7, 6.5, 7.5, 9, 8.5],\n        backgroundColor: '#34C759'\n      }, {\n        label: '心情评分',\n        data: [7, 8, 6, 5, 8, 9, 8],\n        backgroundColor: '#FF9500'\n      }]\n    },\n    metrics: {\n      avgHealthScore: 82,\n      healthScoreTrend: 'up',\n      avgWeight: 69.6,\n      weightTrend: 'down',\n      avgHeartRate: 72,\n      heartRateTrend: 'down',\n      totalExercises: 41,\n      exerciseTrend: 'up'\n    }\n  };\n  useEffect(() => {\n    loadAnalyticsData();\n  }, [selectedMetric, dateRange]);\n  const loadAnalyticsData = async () => {\n    setLoading(true);\n    // 模拟API调用\n    await new Promise(resolve => setTimeout(resolve, 800));\n    setAnalyticsData(mockAnalyticsData);\n    setLoading(false);\n  };\n  const renderTrendIndicator = trend => {\n    if (trend === 'up') {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"trend-indicator positive\",\n        children: [/*#__PURE__*/_jsxDEV(RiseOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), \" \\u4E0A\\u5347\\u8D8B\\u52BF\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this);\n    } else if (trend === 'down') {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"trend-indicator negative\",\n        children: [/*#__PURE__*/_jsxDEV(TrendingDownOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), \" \\u4E0B\\u964D\\u8D8B\\u52BF\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"trend-indicator neutral\",\n      children: \"\\u7A33\\u5B9A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading || !analyticsData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '100px 0'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: 'white',\n          fontSize: '18px'\n        },\n        children: \"\\u52A0\\u8F7D\\u5206\\u6790\\u6570\\u636E\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"slide-up\",\n    children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n      children: \"\\u5065\\u5EB7\\u6570\\u636E\\u5206\\u6790\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ControlPanel, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"control-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"\\u5206\\u6790\\u6307\\u6807\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          value: selectedMetric,\n          onChange: setSelectedMetric,\n          style: {\n            width: 150\n          },\n          children: [/*#__PURE__*/_jsxDEV(Option, {\n            value: \"health_score\",\n            children: \"\\u5065\\u5EB7\\u8BC4\\u5206\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"weight\",\n            children: \"\\u4F53\\u91CD\\u53D8\\u5316\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"heart_rate\",\n            children: \"\\u5FC3\\u7387\\u8D8B\\u52BF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"control-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"\\u65F6\\u95F4\\u8303\\u56F4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n          value: dateRange,\n          onChange: setDateRange,\n          style: {\n            width: 250\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E73\\u5747\\u5065\\u5EB7\\u8BC4\\u5206\",\n            value: analyticsData.metrics.avgHealthScore,\n            prefix: /*#__PURE__*/_jsxDEV(HeartOutlined, {\n              style: {\n                color: '#FF3B30'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), renderTrendIndicator(analyticsData.metrics.healthScoreTrend)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E73\\u5747\\u4F53\\u91CD\",\n            value: analyticsData.metrics.avgWeight,\n            precision: 1,\n            suffix: \"kg\",\n            prefix: /*#__PURE__*/_jsxDEV(TrophyOutlined, {\n              style: {\n                color: '#007AFF'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), renderTrendIndicator(analyticsData.metrics.weightTrend)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E73\\u5747\\u5FC3\\u7387\",\n            value: analyticsData.metrics.avgHeartRate,\n            suffix: \"bpm\",\n            prefix: /*#__PURE__*/_jsxDEV(HeartOutlined, {\n              style: {\n                color: '#34C759'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this), renderTrendIndicator(analyticsData.metrics.heartRateTrend)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FD0\\u52A8\\u6B21\\u6570\",\n            value: analyticsData.metrics.totalExercises,\n            suffix: \"\\u6B21\",\n            prefix: /*#__PURE__*/_jsxDEV(SmileOutlined, {\n              style: {\n                color: '#FF9500'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), renderTrendIndicator(analyticsData.metrics.exerciseTrend)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StyledTabs, {\n      defaultActiveKey: \"trends\",\n      children: [/*#__PURE__*/_jsxDEV(TabPane, {\n        tab: \"\\u8D8B\\u52BF\\u5206\\u6790\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            lg: 16,\n            children: /*#__PURE__*/_jsxDEV(StyledCard, {\n              title: \"\\u5065\\u5EB7\\u6307\\u6807\\u8D8B\\u52BF\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '20px',\n                  color: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: '16px',\n                    fontSize: '16px',\n                    fontWeight: '600'\n                  },\n                  children: selectedMetric === 'health_score' ? '健康评分趋势' : selectedMetric === 'weight' ? '体重变化趋势' : '心率变化趋势'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '12px'\n                  },\n                  children: analyticsData.trends[selectedMetric].labels.map((label, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      padding: '8px 12px',\n                      background: 'rgba(255, 255, 255, 0.1)',\n                      borderRadius: '8px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 386,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontWeight: '600'\n                      },\n                      children: [analyticsData.trends[selectedMetric].datasets[0].data[index], selectedMetric === 'weight' ? ' kg' : selectedMetric === 'heart_rate' ? ' bpm' : '']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 387,\n                      columnNumber: 25\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            lg: 8,\n            children: /*#__PURE__*/_jsxDEV(StyledCard, {\n              title: \"\\u8FD0\\u52A8\\u7C7B\\u578B\\u5206\\u5E03\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '20px',\n                  color: 'white'\n                },\n                children: analyticsData.exerciseStats.labels.map((label, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    padding: '12px 0',\n                    borderBottom: index < analyticsData.exerciseStats.labels.length - 1 ? '1px solid rgba(255, 255, 255, 0.1)' : 'none'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '8px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: '12px',\n                        height: '12px',\n                        borderRadius: '50%',\n                        backgroundColor: analyticsData.exerciseStats.datasets[0].backgroundColor[index]\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 411,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontWeight: '600'\n                    },\n                    children: [analyticsData.exerciseStats.datasets[0].data[index], \" \\u6B21\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)\n      }, \"trends\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: \"\\u6D3B\\u52A8\\u5206\\u6790\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(StyledCard, {\n              title: \"\\u6BCF\\u5468\\u6D3B\\u52A8\\u7EDF\\u8BA1\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '20px',\n                  color: 'white'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(7, 1fr)',\n                    gap: '8px',\n                    marginBottom: '20px'\n                  },\n                  children: analyticsData.weeklyActivity.labels.map((day, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '12px',\n                        marginBottom: '8px',\n                        opacity: 0.8\n                      },\n                      children: day\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 438,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: 'rgba(0, 122, 255, 0.2)',\n                        height: `${analyticsData.weeklyActivity.datasets[0].data[index] * 2}px`,\n                        minHeight: '20px',\n                        borderRadius: '4px',\n                        marginBottom: '4px',\n                        display: 'flex',\n                        alignItems: 'end',\n                        justifyContent: 'center',\n                        fontSize: '10px',\n                        color: 'white'\n                      },\n                      children: analyticsData.weeklyActivity.datasets[0].data[index]\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 439,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '10px',\n                        opacity: 0.6\n                      },\n                      children: \"\\u8FD0\\u52A8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 453,\n                      columnNumber: 25\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    opacity: 0.8\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [\"\\u5E73\\u5747\\u8FD0\\u52A8\\u65F6\\u957F: \", (analyticsData.weeklyActivity.datasets[0].data.reduce((a, b) => a + b, 0) / 7).toFixed(1), \" \\u5206\\u949F/\\u5929\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [\"\\u5E73\\u5747\\u7761\\u7720\\u65F6\\u957F: \", (analyticsData.weeklyActivity.datasets[1].data.reduce((a, b) => a + b, 0) / 7).toFixed(1), \" \\u5C0F\\u65F6/\\u5929\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [\"\\u5E73\\u5747\\u5FC3\\u60C5\\u8BC4\\u5206: \", (analyticsData.weeklyActivity.datasets[2].data.reduce((a, b) => a + b, 0) / 7).toFixed(1), \" \\u5206\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this)\n      }, \"activity\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: \"\\u5FC3\\u60C5\\u5206\\u6790\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            lg: 12,\n            children: /*#__PURE__*/_jsxDEV(StyledCard, {\n              title: \"\\u5FC3\\u60C5\\u5206\\u5E03\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '20px',\n                  color: 'white'\n                },\n                children: analyticsData.moodDistribution.labels.map((mood, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    padding: '12px 0',\n                    borderBottom: index < analyticsData.moodDistribution.labels.length - 1 ? '1px solid rgba(255, 255, 255, 0.1)' : 'none'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '8px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: '12px',\n                        height: '12px',\n                        borderRadius: '50%',\n                        backgroundColor: analyticsData.moodDistribution.datasets[0].backgroundColor[index]\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 483,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: mood\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontWeight: '600'\n                    },\n                    children: [analyticsData.moodDistribution.datasets[0].data[index], \" \\u5929\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            lg: 12,\n            children: /*#__PURE__*/_jsxDEV(StyledCard, {\n              title: \"\\u5FC3\\u60C5\\u7EDF\\u8BA1\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '20px 0'\n                },\n                children: analyticsData.moodDistribution.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: '16px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      marginBottom: '8px',\n                      color: 'white'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: item.mood\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 511,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [item.count, \" \\u5929\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 512,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                    percent: item.count / 28 * 100,\n                    strokeColor: item.color,\n                    showInfo: false\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this)\n      }, \"mood\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 290,\n    columnNumber: 5\n  }, this);\n}\n_s(Analytics, \"wekfA3YopmHFUs/0bxXbK71GOFU=\");\n_c6 = Analytics;\nexport default Analytics;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"PageTitle\");\n$RefreshReg$(_c2, \"StyledCard\");\n$RefreshReg$(_c3, \"ControlPanel\");\n$RefreshReg$(_c4, \"StyledTabs\");\n$RefreshReg$(_c5, \"MetricCard\");\n$RefreshReg$(_c6, \"Analytics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "Select", "DatePicker", "Tabs", "Statistic", "Progress", "<PERSON><PERSON>", "RiseOutlined", "FallOutlined", "HeartOutlined", "TrophyOutlined", "SmileOutlined", "styled", "dayjs", "jsxDEV", "_jsxDEV", "Option", "RangePicker", "TabPane", "Page<PERSON><PERSON>le", "h1", "_c", "StyledCard", "_c2", "ControlPanel", "div", "_c3", "StyledTabs", "_c4", "MetricCard", "_c5", "COLORS", "Analytics", "_s", "selectedMetric", "setSelectedMetric", "date<PERSON><PERSON><PERSON>", "setDateRange", "subtract", "analyticsData", "setAnalyticsData", "loading", "setLoading", "mockAnalyticsData", "trends", "health_score", "labels", "datasets", "label", "data", "borderColor", "backgroundColor", "fill", "tension", "weight", "heart_rate", "exerciseStats", "borderWidth", "moodDistribution", "weeklyActivity", "metrics", "avgHealthScore", "healthScoreTrend", "avgWeight", "weightTrend", "avgHeartRate", "heartRateTrend", "totalExercises", "exerciseTrend", "loadAnalyticsData", "Promise", "resolve", "setTimeout", "renderTrendIndicator", "trend", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "TrendingDownOutlined", "style", "textAlign", "padding", "color", "fontSize", "value", "onChange", "width", "gutter", "marginBottom", "xs", "sm", "lg", "title", "prefix", "precision", "suffix", "defaultActiveKey", "tab", "fontWeight", "display", "flexDirection", "gap", "map", "index", "justifyContent", "background", "borderRadius", "alignItems", "borderBottom", "length", "height", "gridTemplateColumns", "day", "opacity", "minHeight", "reduce", "a", "b", "toFixed", "mood", "item", "count", "percent", "strokeColor", "showInfo", "_c6", "$RefreshReg$"], "sources": ["D:/study/college/junior2/dataBase/system2/frontend/src/pages/Analytics.js"], "sourcesContent": ["/**\n * 数据分析页面\n * 展示健康数据的深度分析和可视化图表\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Select, DatePicker, Tabs, Statistic, Progress, Alert } from 'antd';\n// 暂时不使用图表库，使用简单的数据展示\nimport {\n  RiseOutlined,\n  FallOutlined,\n  HeartOutlined,\n  TrophyOutlined,\n  SmileOutlined,\n} from '@ant-design/icons';\nimport styled from 'styled-components';\nimport dayjs from 'dayjs';\n\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\nconst { TabPane } = Tabs;\n\n// 样式化组件\nconst PageTitle = styled.h1`\n  color: white;\n  font-size: 28px;\n  font-weight: 700;\n  margin-bottom: 24px;\n  text-align: center;\n`;\n\nconst StyledCard = styled(Card)`\n  background: rgba(255, 255, 255, 0.25) !important;\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.18) !important;\n  border-radius: 20px !important;\n  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37) !important;\n  transition: all 0.3s ease !important;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5) !important;\n  }\n  \n  .ant-card-head {\n    background: transparent !important;\n    border-bottom: 1px solid rgba(255, 255, 255, 0.18) !important;\n    \n    .ant-card-head-title {\n      color: white !important;\n      font-weight: 600 !important;\n    }\n  }\n  \n  .ant-card-body {\n    background: transparent !important;\n  }\n`;\n\nconst ControlPanel = styled.div`\n  background: rgba(255, 255, 255, 0.15);\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.18);\n  border-radius: 16px;\n  padding: 20px;\n  margin-bottom: 24px;\n  display: flex;\n  gap: 16px;\n  align-items: center;\n  flex-wrap: wrap;\n  \n  .control-item {\n    display: flex;\n    flex-direction: column;\n    gap: 8px;\n    \n    label {\n      color: rgba(255, 255, 255, 0.8);\n      font-size: 14px;\n      font-weight: 500;\n    }\n  }\n`;\n\nconst StyledTabs = styled(Tabs)`\n  .ant-tabs-nav {\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 12px;\n    padding: 4px;\n    margin-bottom: 24px;\n    \n    .ant-tabs-tab {\n      background: transparent !important;\n      border: none !important;\n      color: rgba(255, 255, 255, 0.7) !important;\n      border-radius: 8px !important;\n      margin: 0 4px !important;\n      \n      &.ant-tabs-tab-active {\n        background: rgba(255, 255, 255, 0.2) !important;\n        color: white !important;\n      }\n    }\n    \n    .ant-tabs-ink-bar {\n      display: none !important;\n    }\n  }\n`;\n\nconst MetricCard = styled(StyledCard)`\n  text-align: center;\n  \n  .ant-statistic-title {\n    color: rgba(255, 255, 255, 0.8) !important;\n    font-size: 14px !important;\n    margin-bottom: 8px !important;\n  }\n  \n  .ant-statistic-content {\n    color: white !important;\n    \n    .ant-statistic-content-value {\n      font-weight: 700 !important;\n    }\n  }\n  \n  .trend-indicator {\n    margin-top: 8px;\n    font-size: 12px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 4px;\n    \n    &.positive {\n      color: #34C759;\n    }\n    \n    &.negative {\n      color: #FF3B30;\n    }\n    \n    &.neutral {\n      color: rgba(255, 255, 255, 0.6);\n    }\n  }\n`;\n\n// 图表颜色配置\nconst COLORS = ['#007AFF', '#34C759', '#FF9500', '#FF3B30', '#AF52DE', '#5AC8FA'];\n\nfunction Analytics() {\n  const [selectedMetric, setSelectedMetric] = useState('health_score');\n  const [dateRange, setDateRange] = useState([dayjs().subtract(30, 'day'), dayjs()]);\n  const [analyticsData, setAnalyticsData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // 模拟分析数据\n  const mockAnalyticsData = {\n    trends: {\n      health_score: {\n        labels: ['1月1日', '1月8日', '1月15日', '1月22日', '1月29日'],\n        datasets: [{\n          label: '健康评分',\n          data: [75, 78, 82, 80, 85],\n          borderColor: '#007AFF',\n          backgroundColor: 'rgba(0, 122, 255, 0.1)',\n          fill: true,\n          tension: 0.4,\n        }]\n      },\n      weight: {\n        labels: ['1月1日', '1月8日', '1月15日', '1月22日', '1月29日'],\n        datasets: [{\n          label: '体重 (kg)',\n          data: [70.5, 70.2, 69.8, 69.5, 69.2],\n          borderColor: '#34C759',\n          backgroundColor: 'rgba(52, 199, 89, 0.1)',\n          fill: true,\n          tension: 0.4,\n        }]\n      },\n      heart_rate: {\n        labels: ['1月1日', '1月8日', '1月15日', '1月22日', '1月29日'],\n        datasets: [{\n          label: '心率 (bpm)',\n          data: [75, 73, 72, 71, 70],\n          borderColor: '#FF3B30',\n          backgroundColor: 'rgba(255, 59, 48, 0.1)',\n          fill: true,\n          tension: 0.4,\n        }]\n      }\n    },\n    exerciseStats: {\n      labels: ['跑步', '游泳', '健身', '瑜伽'],\n      datasets: [{\n        data: [12, 8, 15, 6],\n        backgroundColor: ['#007AFF', '#34C759', '#FF9500', '#AF52DE'],\n        borderWidth: 0,\n      }]\n    },\n    moodDistribution: {\n      labels: ['很好', '好', '一般', '不好'],\n      datasets: [{\n        data: [8, 12, 6, 2],\n        backgroundColor: ['#34C759', '#007AFF', '#FF9500', '#FF3B30'],\n        borderWidth: 0,\n      }]\n    },\n    weeklyActivity: {\n      labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],\n      datasets: [\n        {\n          label: '运动时长(分钟)',\n          data: [45, 30, 60, 0, 40, 90, 75],\n          backgroundColor: '#007AFF',\n        },\n        {\n          label: '睡眠时长(小时)',\n          data: [7.5, 8, 7, 6.5, 7.5, 9, 8.5],\n          backgroundColor: '#34C759',\n        },\n        {\n          label: '心情评分',\n          data: [7, 8, 6, 5, 8, 9, 8],\n          backgroundColor: '#FF9500',\n        }\n      ]\n    },\n    metrics: {\n      avgHealthScore: 82,\n      healthScoreTrend: 'up',\n      avgWeight: 69.6,\n      weightTrend: 'down',\n      avgHeartRate: 72,\n      heartRateTrend: 'down',\n      totalExercises: 41,\n      exerciseTrend: 'up',\n    }\n  };\n\n  useEffect(() => {\n    loadAnalyticsData();\n  }, [selectedMetric, dateRange]);\n\n  const loadAnalyticsData = async () => {\n    setLoading(true);\n    // 模拟API调用\n    await new Promise(resolve => setTimeout(resolve, 800));\n    setAnalyticsData(mockAnalyticsData);\n    setLoading(false);\n  };\n\n\n\n  const renderTrendIndicator = (trend) => {\n    if (trend === 'up') {\n      return (\n        <div className=\"trend-indicator positive\">\n          <RiseOutlined /> 上升趋势\n        </div>\n      );\n    } else if (trend === 'down') {\n      return (\n        <div className=\"trend-indicator negative\">\n          <TrendingDownOutlined /> 下降趋势\n        </div>\n      );\n    }\n    return (\n      <div className=\"trend-indicator neutral\">\n        稳定\n      </div>\n    );\n  };\n\n  if (loading || !analyticsData) {\n    return (\n      <div style={{ textAlign: 'center', padding: '100px 0' }}>\n        <div style={{ color: 'white', fontSize: '18px' }}>加载分析数据中...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"slide-up\">\n      <PageTitle>健康数据分析</PageTitle>\n      \n      {/* 控制面板 */}\n      <ControlPanel>\n        <div className=\"control-item\">\n          <label>分析指标</label>\n          <Select\n            value={selectedMetric}\n            onChange={setSelectedMetric}\n            style={{ width: 150 }}\n          >\n            <Option value=\"health_score\">健康评分</Option>\n            <Option value=\"weight\">体重变化</Option>\n            <Option value=\"heart_rate\">心率趋势</Option>\n          </Select>\n        </div>\n        \n        <div className=\"control-item\">\n          <label>时间范围</label>\n          <RangePicker\n            value={dateRange}\n            onChange={setDateRange}\n            style={{ width: 250 }}\n          />\n        </div>\n      </ControlPanel>\n\n      {/* 关键指标卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} lg={6}>\n          <MetricCard>\n            <Statistic\n              title=\"平均健康评分\"\n              value={analyticsData.metrics.avgHealthScore}\n              prefix={<HeartOutlined style={{ color: '#FF3B30' }} />}\n            />\n            {renderTrendIndicator(analyticsData.metrics.healthScoreTrend)}\n          </MetricCard>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <MetricCard>\n            <Statistic\n              title=\"平均体重\"\n              value={analyticsData.metrics.avgWeight}\n              precision={1}\n              suffix=\"kg\"\n              prefix={<TrophyOutlined style={{ color: '#007AFF' }} />}\n            />\n            {renderTrendIndicator(analyticsData.metrics.weightTrend)}\n          </MetricCard>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <MetricCard>\n            <Statistic\n              title=\"平均心率\"\n              value={analyticsData.metrics.avgHeartRate}\n              suffix=\"bpm\"\n              prefix={<HeartOutlined style={{ color: '#34C759' }} />}\n            />\n            {renderTrendIndicator(analyticsData.metrics.heartRateTrend)}\n          </MetricCard>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <MetricCard>\n            <Statistic\n              title=\"运动次数\"\n              value={analyticsData.metrics.totalExercises}\n              suffix=\"次\"\n              prefix={<SmileOutlined style={{ color: '#FF9500' }} />}\n            />\n            {renderTrendIndicator(analyticsData.metrics.exerciseTrend)}\n          </MetricCard>\n        </Col>\n      </Row>\n\n      {/* 分析图表 */}\n      <StyledTabs defaultActiveKey=\"trends\">\n        <TabPane tab=\"趋势分析\" key=\"trends\">\n          <Row gutter={[16, 16]}>\n            <Col xs={24} lg={16}>\n              <StyledCard title=\"健康指标趋势\">\n                <div style={{ padding: '20px', color: 'white' }}>\n                  <div style={{ marginBottom: '16px', fontSize: '16px', fontWeight: '600' }}>\n                    {selectedMetric === 'health_score' ? '健康评分趋势' :\n                     selectedMetric === 'weight' ? '体重变化趋势' : '心率变化趋势'}\n                  </div>\n                  <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>\n                    {analyticsData.trends[selectedMetric].labels.map((label, index) => (\n                      <div key={index} style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        padding: '8px 12px',\n                        background: 'rgba(255, 255, 255, 0.1)',\n                        borderRadius: '8px'\n                      }}>\n                        <span>{label}</span>\n                        <span style={{ fontWeight: '600' }}>\n                          {analyticsData.trends[selectedMetric].datasets[0].data[index]}\n                          {selectedMetric === 'weight' ? ' kg' : selectedMetric === 'heart_rate' ? ' bpm' : ''}\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </StyledCard>\n            </Col>\n            \n            <Col xs={24} lg={8}>\n              <StyledCard title=\"运动类型分布\">\n                <div style={{ padding: '20px', color: 'white' }}>\n                  {analyticsData.exerciseStats.labels.map((label, index) => (\n                    <div key={index} style={{\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      padding: '12px 0',\n                      borderBottom: index < analyticsData.exerciseStats.labels.length - 1 ?\n                        '1px solid rgba(255, 255, 255, 0.1)' : 'none'\n                    }}>\n                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                        <div style={{\n                          width: '12px',\n                          height: '12px',\n                          borderRadius: '50%',\n                          backgroundColor: analyticsData.exerciseStats.datasets[0].backgroundColor[index]\n                        }}></div>\n                        <span>{label}</span>\n                      </div>\n                      <span style={{ fontWeight: '600' }}>\n                        {analyticsData.exerciseStats.datasets[0].data[index]} 次\n                      </span>\n                    </div>\n                  ))}\n                </div>\n              </StyledCard>\n            </Col>\n          </Row>\n        </TabPane>\n\n        <TabPane tab=\"活动分析\" key=\"activity\">\n          <Row gutter={[16, 16]}>\n            <Col xs={24}>\n              <StyledCard title=\"每周活动统计\">\n                <div style={{ padding: '20px', color: 'white' }}>\n                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: '8px', marginBottom: '20px' }}>\n                    {analyticsData.weeklyActivity.labels.map((day, index) => (\n                      <div key={index} style={{ textAlign: 'center' }}>\n                        <div style={{ fontSize: '12px', marginBottom: '8px', opacity: 0.8 }}>{day}</div>\n                        <div style={{\n                          background: 'rgba(0, 122, 255, 0.2)',\n                          height: `${analyticsData.weeklyActivity.datasets[0].data[index] * 2}px`,\n                          minHeight: '20px',\n                          borderRadius: '4px',\n                          marginBottom: '4px',\n                          display: 'flex',\n                          alignItems: 'end',\n                          justifyContent: 'center',\n                          fontSize: '10px',\n                          color: 'white'\n                        }}>\n                          {analyticsData.weeklyActivity.datasets[0].data[index]}\n                        </div>\n                        <div style={{ fontSize: '10px', opacity: 0.6 }}>运动</div>\n                      </div>\n                    ))}\n                  </div>\n                  <div style={{ fontSize: '14px', opacity: 0.8 }}>\n                    <div>平均运动时长: {(analyticsData.weeklyActivity.datasets[0].data.reduce((a, b) => a + b, 0) / 7).toFixed(1)} 分钟/天</div>\n                    <div>平均睡眠时长: {(analyticsData.weeklyActivity.datasets[1].data.reduce((a, b) => a + b, 0) / 7).toFixed(1)} 小时/天</div>\n                    <div>平均心情评分: {(analyticsData.weeklyActivity.datasets[2].data.reduce((a, b) => a + b, 0) / 7).toFixed(1)} 分</div>\n                  </div>\n                </div>\n              </StyledCard>\n            </Col>\n          </Row>\n        </TabPane>\n\n        <TabPane tab=\"心情分析\" key=\"mood\">\n          <Row gutter={[16, 16]}>\n            <Col xs={24} lg={12}>\n              <StyledCard title=\"心情分布\">\n                <div style={{ padding: '20px', color: 'white' }}>\n                  {analyticsData.moodDistribution.labels.map((mood, index) => (\n                    <div key={index} style={{\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      padding: '12px 0',\n                      borderBottom: index < analyticsData.moodDistribution.labels.length - 1 ?\n                        '1px solid rgba(255, 255, 255, 0.1)' : 'none'\n                    }}>\n                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                        <div style={{\n                          width: '12px',\n                          height: '12px',\n                          borderRadius: '50%',\n                          backgroundColor: analyticsData.moodDistribution.datasets[0].backgroundColor[index]\n                        }}></div>\n                        <span>{mood}</span>\n                      </div>\n                      <span style={{ fontWeight: '600' }}>\n                        {analyticsData.moodDistribution.datasets[0].data[index]} 天\n                      </span>\n                    </div>\n                  ))}\n                </div>\n              </StyledCard>\n            </Col>\n            \n            <Col xs={24} lg={12}>\n              <StyledCard title=\"心情统计\">\n                <div style={{ padding: '20px 0' }}>\n                  {analyticsData.moodDistribution.map((item, index) => (\n                    <div key={index} style={{ marginBottom: '16px' }}>\n                      <div style={{ \n                        display: 'flex', \n                        justifyContent: 'space-between', \n                        marginBottom: '8px',\n                        color: 'white'\n                      }}>\n                        <span>{item.mood}</span>\n                        <span>{item.count} 天</span>\n                      </div>\n                      <Progress \n                        percent={(item.count / 28) * 100} \n                        strokeColor={item.color}\n                        showInfo={false}\n                      />\n                    </div>\n                  ))}\n                </div>\n              </StyledCard>\n            </Col>\n          </Row>\n        </TabPane>\n      </StyledTabs>\n    </div>\n  );\n}\n\nexport default Analytics;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,MAAM;AAC3F;AACA,SACEC,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,cAAc,EACdC,aAAa,QACR,mBAAmB;AAC1B,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC;AAAO,CAAC,GAAGf,MAAM;AACzB,MAAM;EAAEgB;AAAY,CAAC,GAAGf,UAAU;AAClC,MAAM;EAAEgB;AAAQ,CAAC,GAAGf,IAAI;;AAExB;AACA,MAAMgB,SAAS,GAAGP,MAAM,CAACQ,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,SAAS;AAQf,MAAMG,UAAU,GAAGV,MAAM,CAACZ,IAAI,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GA3BID,UAAU;AA6BhB,MAAME,YAAY,GAAGZ,MAAM,CAACa,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAxBIF,YAAY;AA0BlB,MAAMG,UAAU,GAAGf,MAAM,CAACT,IAAI,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GAxBID,UAAU;AA0BhB,MAAME,UAAU,GAAGjB,MAAM,CAACU,UAAU,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAQ,GAAA,GAvCMD,UAAU;AAwChB,MAAME,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;AAEjF,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAC,cAAc,CAAC;EACpE,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,CAACiB,KAAK,CAAC,CAAC,CAACyB,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,EAAEzB,KAAK,CAAC,CAAC,CAAC,CAAC;EAClF,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM+C,iBAAiB,GAAG;IACxBC,MAAM,EAAE;MACNC,YAAY,EAAE;QACZC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;QACnDC,QAAQ,EAAE,CAAC;UACTC,KAAK,EAAE,MAAM;UACbC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;UAC1BC,WAAW,EAAE,SAAS;UACtBC,eAAe,EAAE,wBAAwB;UACzCC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE;QACX,CAAC;MACH,CAAC;MACDC,MAAM,EAAE;QACNR,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;QACnDC,QAAQ,EAAE,CAAC;UACTC,KAAK,EAAE,SAAS;UAChBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UACpCC,WAAW,EAAE,SAAS;UACtBC,eAAe,EAAE,wBAAwB;UACzCC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE;QACX,CAAC;MACH,CAAC;MACDE,UAAU,EAAE;QACVT,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;QACnDC,QAAQ,EAAE,CAAC;UACTC,KAAK,EAAE,UAAU;UACjBC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;UAC1BC,WAAW,EAAE,SAAS;UACtBC,eAAe,EAAE,wBAAwB;UACzCC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC;IACDG,aAAa,EAAE;MACbV,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAChCC,QAAQ,EAAE,CAAC;QACTE,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACpBE,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;QAC7DM,WAAW,EAAE;MACf,CAAC;IACH,CAAC;IACDC,gBAAgB,EAAE;MAChBZ,MAAM,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;MAC/BC,QAAQ,EAAE,CAAC;QACTE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QACnBE,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;QAC7DM,WAAW,EAAE;MACf,CAAC;IACH,CAAC;IACDE,cAAc,EAAE;MACdb,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAClDC,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QACjCE,eAAe,EAAE;MACnB,CAAC,EACD;QACEH,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;QACnCE,eAAe,EAAE;MACnB,CAAC,EACD;QACEH,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3BE,eAAe,EAAE;MACnB,CAAC;IAEL,CAAC;IACDS,OAAO,EAAE;MACPC,cAAc,EAAE,EAAE;MAClBC,gBAAgB,EAAE,IAAI;MACtBC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,MAAM;MACnBC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,MAAM;MACtBC,cAAc,EAAE,EAAE;MAClBC,aAAa,EAAE;IACjB;EACF,CAAC;EAEDvE,SAAS,CAAC,MAAM;IACdwE,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACnC,cAAc,EAAEE,SAAS,CAAC,CAAC;EAE/B,MAAMiC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC3B,UAAU,CAAC,IAAI,CAAC;IAChB;IACA,MAAM,IAAI4B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD/B,gBAAgB,CAACG,iBAAiB,CAAC;IACnCD,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAID,MAAM+B,oBAAoB,GAAIC,KAAK,IAAK;IACtC,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClB,oBACE3D,OAAA;QAAK4D,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvC7D,OAAA,CAACR,YAAY;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,6BAClB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAEV,CAAC,MAAM,IAAIN,KAAK,KAAK,MAAM,EAAE;MAC3B,oBACE3D,OAAA;QAAK4D,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvC7D,OAAA,CAACkE,oBAAoB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,6BAC1B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAEV;IACA,oBACEjE,OAAA;MAAK4D,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAEzC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV,CAAC;EAED,IAAIvC,OAAO,IAAI,CAACF,aAAa,EAAE;IAC7B,oBACExB,OAAA;MAAKmE,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAU,CAAE;MAAAR,QAAA,eACtD7D,OAAA;QAAKmE,KAAK,EAAE;UAAEG,KAAK,EAAE,OAAO;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAV,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC;EAEV;EAEA,oBACEjE,OAAA;IAAK4D,SAAS,EAAC,UAAU;IAAAC,QAAA,gBACvB7D,OAAA,CAACI,SAAS;MAAAyD,QAAA,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAW,CAAC,eAG7BjE,OAAA,CAACS,YAAY;MAAAoD,QAAA,gBACX7D,OAAA;QAAK4D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B7D,OAAA;UAAA6D,QAAA,EAAO;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnBjE,OAAA,CAACd,MAAM;UACLsF,KAAK,EAAErD,cAAe;UACtBsD,QAAQ,EAAErD,iBAAkB;UAC5B+C,KAAK,EAAE;YAAEO,KAAK,EAAE;UAAI,CAAE;UAAAb,QAAA,gBAEtB7D,OAAA,CAACC,MAAM;YAACuE,KAAK,EAAC,cAAc;YAAAX,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CjE,OAAA,CAACC,MAAM;YAACuE,KAAK,EAAC,QAAQ;YAAAX,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpCjE,OAAA,CAACC,MAAM;YAACuE,KAAK,EAAC,YAAY;YAAAX,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENjE,OAAA;QAAK4D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B7D,OAAA;UAAA6D,QAAA,EAAO;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnBjE,OAAA,CAACE,WAAW;UACVsE,KAAK,EAAEnD,SAAU;UACjBoD,QAAQ,EAAEnD,YAAa;UACvB6C,KAAK,EAAE;YAAEO,KAAK,EAAE;UAAI;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAGfjE,OAAA,CAACjB,GAAG;MAAC4F,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACR,KAAK,EAAE;QAAES,YAAY,EAAE;MAAG,CAAE;MAAAf,QAAA,gBACjD7D,OAAA,CAAChB,GAAG;QAAC6F,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzB7D,OAAA,CAACc,UAAU;UAAA+C,QAAA,gBACT7D,OAAA,CAACX,SAAS;YACR2F,KAAK,EAAC,sCAAQ;YACdR,KAAK,EAAEhD,aAAa,CAACqB,OAAO,CAACC,cAAe;YAC5CmC,MAAM,eAAEjF,OAAA,CAACN,aAAa;cAACyE,KAAK,EAAE;gBAAEG,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,EACDP,oBAAoB,CAAClC,aAAa,CAACqB,OAAO,CAACE,gBAAgB,CAAC;QAAA;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNjE,OAAA,CAAChB,GAAG;QAAC6F,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzB7D,OAAA,CAACc,UAAU;UAAA+C,QAAA,gBACT7D,OAAA,CAACX,SAAS;YACR2F,KAAK,EAAC,0BAAM;YACZR,KAAK,EAAEhD,aAAa,CAACqB,OAAO,CAACG,SAAU;YACvCkC,SAAS,EAAE,CAAE;YACbC,MAAM,EAAC,IAAI;YACXF,MAAM,eAAEjF,OAAA,CAACL,cAAc;cAACwE,KAAK,EAAE;gBAAEG,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,EACDP,oBAAoB,CAAClC,aAAa,CAACqB,OAAO,CAACI,WAAW,CAAC;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNjE,OAAA,CAAChB,GAAG;QAAC6F,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzB7D,OAAA,CAACc,UAAU;UAAA+C,QAAA,gBACT7D,OAAA,CAACX,SAAS;YACR2F,KAAK,EAAC,0BAAM;YACZR,KAAK,EAAEhD,aAAa,CAACqB,OAAO,CAACK,YAAa;YAC1CiC,MAAM,EAAC,KAAK;YACZF,MAAM,eAAEjF,OAAA,CAACN,aAAa;cAACyE,KAAK,EAAE;gBAAEG,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,EACDP,oBAAoB,CAAClC,aAAa,CAACqB,OAAO,CAACM,cAAc,CAAC;QAAA;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNjE,OAAA,CAAChB,GAAG;QAAC6F,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzB7D,OAAA,CAACc,UAAU;UAAA+C,QAAA,gBACT7D,OAAA,CAACX,SAAS;YACR2F,KAAK,EAAC,0BAAM;YACZR,KAAK,EAAEhD,aAAa,CAACqB,OAAO,CAACO,cAAe;YAC5C+B,MAAM,EAAC,QAAG;YACVF,MAAM,eAAEjF,OAAA,CAACJ,aAAa;cAACuE,KAAK,EAAE;gBAAEG,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,EACDP,oBAAoB,CAAClC,aAAa,CAACqB,OAAO,CAACQ,aAAa,CAAC;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjE,OAAA,CAACY,UAAU;MAACwE,gBAAgB,EAAC,QAAQ;MAAAvB,QAAA,gBACnC7D,OAAA,CAACG,OAAO;QAACkF,GAAG,EAAC,0BAAM;QAAAxB,QAAA,eACjB7D,OAAA,CAACjB,GAAG;UAAC4F,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAd,QAAA,gBACpB7D,OAAA,CAAChB,GAAG;YAAC6F,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAAlB,QAAA,eAClB7D,OAAA,CAACO,UAAU;cAACyE,KAAK,EAAC,sCAAQ;cAAAnB,QAAA,eACxB7D,OAAA;gBAAKmE,KAAK,EAAE;kBAAEE,OAAO,EAAE,MAAM;kBAAEC,KAAK,EAAE;gBAAQ,CAAE;gBAAAT,QAAA,gBAC9C7D,OAAA;kBAAKmE,KAAK,EAAE;oBAAES,YAAY,EAAE,MAAM;oBAAEL,QAAQ,EAAE,MAAM;oBAAEe,UAAU,EAAE;kBAAM,CAAE;kBAAAzB,QAAA,EACvE1C,cAAc,KAAK,cAAc,GAAG,QAAQ,GAC5CA,cAAc,KAAK,QAAQ,GAAG,QAAQ,GAAG;gBAAQ;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACNjE,OAAA;kBAAKmE,KAAK,EAAE;oBAAEoB,OAAO,EAAE,MAAM;oBAAEC,aAAa,EAAE,QAAQ;oBAAEC,GAAG,EAAE;kBAAO,CAAE;kBAAA5B,QAAA,EACnErC,aAAa,CAACK,MAAM,CAACV,cAAc,CAAC,CAACY,MAAM,CAAC2D,GAAG,CAAC,CAACzD,KAAK,EAAE0D,KAAK,kBAC5D3F,OAAA;oBAAiBmE,KAAK,EAAE;sBACtBoB,OAAO,EAAE,MAAM;sBACfK,cAAc,EAAE,eAAe;sBAC/BvB,OAAO,EAAE,UAAU;sBACnBwB,UAAU,EAAE,0BAA0B;sBACtCC,YAAY,EAAE;oBAChB,CAAE;oBAAAjC,QAAA,gBACA7D,OAAA;sBAAA6D,QAAA,EAAO5B;oBAAK;sBAAA6B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACpBjE,OAAA;sBAAMmE,KAAK,EAAE;wBAAEmB,UAAU,EAAE;sBAAM,CAAE;sBAAAzB,QAAA,GAChCrC,aAAa,CAACK,MAAM,CAACV,cAAc,CAAC,CAACa,QAAQ,CAAC,CAAC,CAAC,CAACE,IAAI,CAACyD,KAAK,CAAC,EAC5DxE,cAAc,KAAK,QAAQ,GAAG,KAAK,GAAGA,cAAc,KAAK,YAAY,GAAG,MAAM,GAAG,EAAE;oBAAA;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF,CAAC;kBAAA,GAXC0B,KAAK;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAYV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENjE,OAAA,CAAChB,GAAG;YAAC6F,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAlB,QAAA,eACjB7D,OAAA,CAACO,UAAU;cAACyE,KAAK,EAAC,sCAAQ;cAAAnB,QAAA,eACxB7D,OAAA;gBAAKmE,KAAK,EAAE;kBAAEE,OAAO,EAAE,MAAM;kBAAEC,KAAK,EAAE;gBAAQ,CAAE;gBAAAT,QAAA,EAC7CrC,aAAa,CAACiB,aAAa,CAACV,MAAM,CAAC2D,GAAG,CAAC,CAACzD,KAAK,EAAE0D,KAAK,kBACnD3F,OAAA;kBAAiBmE,KAAK,EAAE;oBACtBoB,OAAO,EAAE,MAAM;oBACfK,cAAc,EAAE,eAAe;oBAC/BG,UAAU,EAAE,QAAQ;oBACpB1B,OAAO,EAAE,QAAQ;oBACjB2B,YAAY,EAAEL,KAAK,GAAGnE,aAAa,CAACiB,aAAa,CAACV,MAAM,CAACkE,MAAM,GAAG,CAAC,GACjE,oCAAoC,GAAG;kBAC3C,CAAE;kBAAApC,QAAA,gBACA7D,OAAA;oBAAKmE,KAAK,EAAE;sBAAEoB,OAAO,EAAE,MAAM;sBAAEQ,UAAU,EAAE,QAAQ;sBAAEN,GAAG,EAAE;oBAAM,CAAE;oBAAA5B,QAAA,gBAChE7D,OAAA;sBAAKmE,KAAK,EAAE;wBACVO,KAAK,EAAE,MAAM;wBACbwB,MAAM,EAAE,MAAM;wBACdJ,YAAY,EAAE,KAAK;wBACnB1D,eAAe,EAAEZ,aAAa,CAACiB,aAAa,CAACT,QAAQ,CAAC,CAAC,CAAC,CAACI,eAAe,CAACuD,KAAK;sBAChF;oBAAE;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACTjE,OAAA;sBAAA6D,QAAA,EAAO5B;oBAAK;sBAAA6B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACNjE,OAAA;oBAAMmE,KAAK,EAAE;sBAAEmB,UAAU,EAAE;oBAAM,CAAE;oBAAAzB,QAAA,GAChCrC,aAAa,CAACiB,aAAa,CAACT,QAAQ,CAAC,CAAC,CAAC,CAACE,IAAI,CAACyD,KAAK,CAAC,EAAC,SACvD;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GAnBC0B,KAAK;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GA3DgB,QAAQ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4DvB,CAAC,eAEVjE,OAAA,CAACG,OAAO;QAACkF,GAAG,EAAC,0BAAM;QAAAxB,QAAA,eACjB7D,OAAA,CAACjB,GAAG;UAAC4F,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAd,QAAA,eACpB7D,OAAA,CAAChB,GAAG;YAAC6F,EAAE,EAAE,EAAG;YAAAhB,QAAA,eACV7D,OAAA,CAACO,UAAU;cAACyE,KAAK,EAAC,sCAAQ;cAAAnB,QAAA,eACxB7D,OAAA;gBAAKmE,KAAK,EAAE;kBAAEE,OAAO,EAAE,MAAM;kBAAEC,KAAK,EAAE;gBAAQ,CAAE;gBAAAT,QAAA,gBAC9C7D,OAAA;kBAAKmE,KAAK,EAAE;oBAAEoB,OAAO,EAAE,MAAM;oBAAEY,mBAAmB,EAAE,gBAAgB;oBAAEV,GAAG,EAAE,KAAK;oBAAEb,YAAY,EAAE;kBAAO,CAAE;kBAAAf,QAAA,EACtGrC,aAAa,CAACoB,cAAc,CAACb,MAAM,CAAC2D,GAAG,CAAC,CAACU,GAAG,EAAET,KAAK,kBAClD3F,OAAA;oBAAiBmE,KAAK,EAAE;sBAAEC,SAAS,EAAE;oBAAS,CAAE;oBAAAP,QAAA,gBAC9C7D,OAAA;sBAAKmE,KAAK,EAAE;wBAAEI,QAAQ,EAAE,MAAM;wBAAEK,YAAY,EAAE,KAAK;wBAAEyB,OAAO,EAAE;sBAAI,CAAE;sBAAAxC,QAAA,EAAEuC;oBAAG;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChFjE,OAAA;sBAAKmE,KAAK,EAAE;wBACV0B,UAAU,EAAE,wBAAwB;wBACpCK,MAAM,EAAE,GAAG1E,aAAa,CAACoB,cAAc,CAACZ,QAAQ,CAAC,CAAC,CAAC,CAACE,IAAI,CAACyD,KAAK,CAAC,GAAG,CAAC,IAAI;wBACvEW,SAAS,EAAE,MAAM;wBACjBR,YAAY,EAAE,KAAK;wBACnBlB,YAAY,EAAE,KAAK;wBACnBW,OAAO,EAAE,MAAM;wBACfQ,UAAU,EAAE,KAAK;wBACjBH,cAAc,EAAE,QAAQ;wBACxBrB,QAAQ,EAAE,MAAM;wBAChBD,KAAK,EAAE;sBACT,CAAE;sBAAAT,QAAA,EACCrC,aAAa,CAACoB,cAAc,CAACZ,QAAQ,CAAC,CAAC,CAAC,CAACE,IAAI,CAACyD,KAAK;oBAAC;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACNjE,OAAA;sBAAKmE,KAAK,EAAE;wBAAEI,QAAQ,EAAE,MAAM;wBAAE8B,OAAO,EAAE;sBAAI,CAAE;sBAAAxC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA,GAhBhD0B,KAAK;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiBV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNjE,OAAA;kBAAKmE,KAAK,EAAE;oBAAEI,QAAQ,EAAE,MAAM;oBAAE8B,OAAO,EAAE;kBAAI,CAAE;kBAAAxC,QAAA,gBAC7C7D,OAAA;oBAAA6D,QAAA,GAAK,wCAAQ,EAAC,CAACrC,aAAa,CAACoB,cAAc,CAACZ,QAAQ,CAAC,CAAC,CAAC,CAACE,IAAI,CAACqE,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,sBAAK;kBAAA;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnHjE,OAAA;oBAAA6D,QAAA,GAAK,wCAAQ,EAAC,CAACrC,aAAa,CAACoB,cAAc,CAACZ,QAAQ,CAAC,CAAC,CAAC,CAACE,IAAI,CAACqE,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,sBAAK;kBAAA;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnHjE,OAAA;oBAAA6D,QAAA,GAAK,wCAAQ,EAAC,CAACrC,aAAa,CAACoB,cAAc,CAACZ,QAAQ,CAAC,CAAC,CAAC,CAACE,IAAI,CAACqE,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,SAAE;kBAAA;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAnCgB,UAAU;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoCzB,CAAC,eAEVjE,OAAA,CAACG,OAAO;QAACkF,GAAG,EAAC,0BAAM;QAAAxB,QAAA,eACjB7D,OAAA,CAACjB,GAAG;UAAC4F,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAd,QAAA,gBACpB7D,OAAA,CAAChB,GAAG;YAAC6F,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAAlB,QAAA,eAClB7D,OAAA,CAACO,UAAU;cAACyE,KAAK,EAAC,0BAAM;cAAAnB,QAAA,eACtB7D,OAAA;gBAAKmE,KAAK,EAAE;kBAAEE,OAAO,EAAE,MAAM;kBAAEC,KAAK,EAAE;gBAAQ,CAAE;gBAAAT,QAAA,EAC7CrC,aAAa,CAACmB,gBAAgB,CAACZ,MAAM,CAAC2D,GAAG,CAAC,CAACiB,IAAI,EAAEhB,KAAK,kBACrD3F,OAAA;kBAAiBmE,KAAK,EAAE;oBACtBoB,OAAO,EAAE,MAAM;oBACfK,cAAc,EAAE,eAAe;oBAC/BG,UAAU,EAAE,QAAQ;oBACpB1B,OAAO,EAAE,QAAQ;oBACjB2B,YAAY,EAAEL,KAAK,GAAGnE,aAAa,CAACmB,gBAAgB,CAACZ,MAAM,CAACkE,MAAM,GAAG,CAAC,GACpE,oCAAoC,GAAG;kBAC3C,CAAE;kBAAApC,QAAA,gBACA7D,OAAA;oBAAKmE,KAAK,EAAE;sBAAEoB,OAAO,EAAE,MAAM;sBAAEQ,UAAU,EAAE,QAAQ;sBAAEN,GAAG,EAAE;oBAAM,CAAE;oBAAA5B,QAAA,gBAChE7D,OAAA;sBAAKmE,KAAK,EAAE;wBACVO,KAAK,EAAE,MAAM;wBACbwB,MAAM,EAAE,MAAM;wBACdJ,YAAY,EAAE,KAAK;wBACnB1D,eAAe,EAAEZ,aAAa,CAACmB,gBAAgB,CAACX,QAAQ,CAAC,CAAC,CAAC,CAACI,eAAe,CAACuD,KAAK;sBACnF;oBAAE;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACTjE,OAAA;sBAAA6D,QAAA,EAAO8C;oBAAI;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,eACNjE,OAAA;oBAAMmE,KAAK,EAAE;sBAAEmB,UAAU,EAAE;oBAAM,CAAE;oBAAAzB,QAAA,GAChCrC,aAAa,CAACmB,gBAAgB,CAACX,QAAQ,CAAC,CAAC,CAAC,CAACE,IAAI,CAACyD,KAAK,CAAC,EAAC,SAC1D;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GAnBC0B,KAAK;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENjE,OAAA,CAAChB,GAAG;YAAC6F,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAAlB,QAAA,eAClB7D,OAAA,CAACO,UAAU;cAACyE,KAAK,EAAC,0BAAM;cAAAnB,QAAA,eACtB7D,OAAA;gBAAKmE,KAAK,EAAE;kBAAEE,OAAO,EAAE;gBAAS,CAAE;gBAAAR,QAAA,EAC/BrC,aAAa,CAACmB,gBAAgB,CAAC+C,GAAG,CAAC,CAACkB,IAAI,EAAEjB,KAAK,kBAC9C3F,OAAA;kBAAiBmE,KAAK,EAAE;oBAAES,YAAY,EAAE;kBAAO,CAAE;kBAAAf,QAAA,gBAC/C7D,OAAA;oBAAKmE,KAAK,EAAE;sBACVoB,OAAO,EAAE,MAAM;sBACfK,cAAc,EAAE,eAAe;sBAC/BhB,YAAY,EAAE,KAAK;sBACnBN,KAAK,EAAE;oBACT,CAAE;oBAAAT,QAAA,gBACA7D,OAAA;sBAAA6D,QAAA,EAAO+C,IAAI,CAACD;oBAAI;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACxBjE,OAAA;sBAAA6D,QAAA,GAAO+C,IAAI,CAACC,KAAK,EAAC,SAAE;oBAAA;sBAAA/C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACNjE,OAAA,CAACV,QAAQ;oBACPwH,OAAO,EAAGF,IAAI,CAACC,KAAK,GAAG,EAAE,GAAI,GAAI;oBACjCE,WAAW,EAAEH,IAAI,CAACtC,KAAM;oBACxB0C,QAAQ,EAAE;kBAAM;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA,GAdM0B,KAAK;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAeV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAxDgB,MAAM;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAyDrB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV;AAAC/C,EAAA,CAtXQD,SAAS;AAAAgG,GAAA,GAAThG,SAAS;AAwXlB,eAAeA,SAAS;AAAC,IAAAX,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAkG,GAAA;AAAAC,YAAA,CAAA5G,EAAA;AAAA4G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}