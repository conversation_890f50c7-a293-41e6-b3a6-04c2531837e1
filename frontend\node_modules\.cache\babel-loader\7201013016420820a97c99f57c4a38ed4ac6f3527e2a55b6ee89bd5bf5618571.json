{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport rules from \"../rule\";\nvar required = function required(rule, value, callback, source, options) {\n  var errors = [];\n  var type = Array.isArray(value) ? 'array' : _typeof(value);\n  rules.required(rule, value, source, errors, options, type);\n  callback(errors);\n};\nexport default required;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}