[{"D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\index.js": "1", "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\App.js": "2", "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\pages\\Dashboard.js": "3", "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\pages\\Analytics.js": "4", "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\pages\\Profile.js": "5", "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\pages\\HealthRecords.js": "6", "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\components\\Layout\\AppSidebar.js": "7", "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\components\\Layout\\AppHeader.js": "8"}, {"size": 2164, "mtime": 1751031960911, "results": "9", "hashOfConfig": "10"}, {"size": 3090, "mtime": 1751034133987, "results": "11", "hashOfConfig": "10"}, {"size": 8028, "mtime": 1751032166612, "results": "12", "hashOfConfig": "10"}, {"size": 17417, "mtime": 1751034728716, "results": "13", "hashOfConfig": "10"}, {"size": 16853, "mtime": 1751033037243, "results": "14", "hashOfConfig": "10"}, {"size": 13373, "mtime": 1751032853019, "results": "15", "hashOfConfig": "10"}, {"size": 3454, "mtime": 1751032116753, "results": "16", "hashOfConfig": "10"}, {"size": 3368, "mtime": 1751032047675, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9pvl3o", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\index.js", [], [], "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\App.js", [], [], "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\pages\\Dashboard.js", ["42", "43"], [], "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\pages\\Analytics.js", ["44", "45", "46", "47", "48", "49", "50"], [], "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\pages\\Profile.js", [], [], "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\pages\\HealthRecords.js", ["51", "52"], [], "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\components\\Layout\\AppSidebar.js", [], [], "D:\\study\\college\\junior2\\dataBase\\system2\\frontend\\src\\components\\Layout\\AppHeader.js", ["53"], [], {"ruleId": "54", "severity": 1, "message": "55", "line": 13, "column": 3, "nodeType": "56", "messageId": "57", "endLine": 13, "endColumn": 18}, {"ruleId": "54", "severity": 1, "message": "58", "line": 14, "column": 3, "nodeType": "56", "messageId": "57", "endLine": 14, "endColumn": 20}, {"ruleId": "54", "severity": 1, "message": "59", "line": 7, "column": 73, "nodeType": "56", "messageId": "57", "endLine": 7, "endColumn": 78}, {"ruleId": "54", "severity": 1, "message": "60", "line": 10, "column": 3, "nodeType": "56", "messageId": "57", "endLine": 10, "endColumn": 15}, {"ruleId": "54", "severity": 1, "message": "61", "line": 11, "column": 3, "nodeType": "56", "messageId": "57", "endLine": 11, "endColumn": 15}, {"ruleId": "54", "severity": 1, "message": "62", "line": 153, "column": 7, "nodeType": "56", "messageId": "57", "endLine": 153, "endColumn": 13}, {"ruleId": "63", "severity": 1, "message": "64", "line": 248, "column": 6, "nodeType": "65", "endLine": 248, "endColumn": 33, "suggestions": "66"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 264, "column": 12, "nodeType": "69", "messageId": "70", "endLine": 264, "endColumn": 30}, {"ruleId": "67", "severity": 2, "message": "71", "line": 270, "column": 12, "nodeType": "69", "messageId": "70", "endLine": 270, "endColumn": 32}, {"ruleId": "54", "severity": 1, "message": "72", "line": 16, "column": 3, "nodeType": "56", "messageId": "57", "endLine": 16, "endColumn": 14}, {"ruleId": "63", "severity": 1, "message": "73", "line": 185, "column": 6, "nodeType": "65", "endLine": 185, "endColumn": 8, "suggestions": "74"}, {"ruleId": "54", "severity": 1, "message": "75", "line": 7, "column": 30, "nodeType": "56", "messageId": "57", "endLine": 7, "endColumn": 35}, "no-unused-vars", "'ArrowUpOutlined' is defined but never used.", "Identifier", "unusedVar", "'ArrowDownOutlined' is defined but never used.", "'Alert' is defined but never used.", "'RiseOutlined' is defined but never used.", "'FallOutlined' is defined but never used.", "'COLORS' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadAnalyticsData'. Either include it or remove the dependency array.", "ArrayExpression", ["76"], "react/jsx-no-undef", "'TrendingUpOutlined' is not defined.", "JSXIdentifier", "undefined", "'TrendingDownOutlined' is not defined.", "'InputNumber' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadRecords'. Either include it or remove the dependency array.", ["77"], "'Space' is defined but never used.", {"desc": "78", "fix": "79"}, {"desc": "80", "fix": "81"}, "Update the dependencies array to be: [selectedMetric, dateRange, loadAnalyticsData]", {"range": "82", "text": "83"}, "Update the dependencies array to be: [loadRecords]", {"range": "84", "text": "85"}, [5851, 5878], "[selectedMetric, dateRange, loadAnalyticsData]", [4252, 4254], "[loadRecords]"]