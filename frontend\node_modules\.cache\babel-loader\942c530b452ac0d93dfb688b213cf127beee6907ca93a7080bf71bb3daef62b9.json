{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nexport default function Arrow(props) {\n  var prefixCls = props.prefixCls,\n    align = props.align,\n    arrow = props.arrow,\n    arrowPos = props.arrowPos;\n  var _ref = arrow || {},\n    className = _ref.className,\n    content = _ref.content;\n  var _arrowPos$x = arrowPos.x,\n    x = _arrowPos$x === void 0 ? 0 : _arrowPos$x,\n    _arrowPos$y = arrowPos.y,\n    y = _arrowPos$y === void 0 ? 0 : _arrowPos$y;\n  var arrowRef = React.useRef();\n\n  // Skip if no align\n  if (!align || !align.points) {\n    return null;\n  }\n  var alignStyle = {\n    position: 'absolute'\n  };\n\n  // Skip if no need to align\n  if (align.autoArrow !== false) {\n    var popupPoints = align.points[0];\n    var targetPoints = align.points[1];\n    var popupTB = popupPoints[0];\n    var popupLR = popupPoints[1];\n    var targetTB = targetPoints[0];\n    var targetLR = targetPoints[1];\n\n    // Top & Bottom\n    if (popupTB === targetTB || !['t', 'b'].includes(popupTB)) {\n      alignStyle.top = y;\n    } else if (popupTB === 't') {\n      alignStyle.top = 0;\n    } else {\n      alignStyle.bottom = 0;\n    }\n\n    // Left & Right\n    if (popupLR === targetLR || !['l', 'r'].includes(popupLR)) {\n      alignStyle.left = x;\n    } else if (popupLR === 'l') {\n      alignStyle.left = 0;\n    } else {\n      alignStyle.right = 0;\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: arrowRef,\n    className: classNames(\"\".concat(prefixCls, \"-arrow\"), className),\n    style: alignStyle\n  }, content);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}