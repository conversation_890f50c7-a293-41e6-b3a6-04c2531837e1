{"ast": null, "code": "export function isImageValid(src) {\n  return new Promise(function (resolve) {\n    if (!src) {\n      resolve(false);\n      return;\n    }\n    var img = document.createElement('img');\n    img.onerror = function () {\n      return resolve(false);\n    };\n    img.onload = function () {\n      return resolve(true);\n    };\n    img.src = src;\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}