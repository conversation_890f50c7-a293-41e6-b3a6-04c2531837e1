{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { isDOM } from \"rc-util/es/Dom/findDOMNode\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { collectScroller, getVisibleArea, getWin, toNum } from \"../util\";\nfunction getUnitOffset(size) {\n  var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var offsetStr = \"\".concat(offset);\n  var cells = offsetStr.match(/^(.*)\\%$/);\n  if (cells) {\n    return size * (parseFloat(cells[1]) / 100);\n  }\n  return parseFloat(offsetStr);\n}\nfunction getNumberOffset(rect, offset) {\n  var _ref = offset || [],\n    _ref2 = _slicedToArray(_ref, 2),\n    offsetX = _ref2[0],\n    offsetY = _ref2[1];\n  return [getUnitOffset(rect.width, offsetX), getUnitOffset(rect.height, offsetY)];\n}\nfunction splitPoints() {\n  var points = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  return [points[0], points[1]];\n}\nfunction getAlignPoint(rect, points) {\n  var topBottom = points[0];\n  var leftRight = points[1];\n  var x;\n  var y;\n\n  // Top & Bottom\n  if (topBottom === 't') {\n    y = rect.y;\n  } else if (topBottom === 'b') {\n    y = rect.y + rect.height;\n  } else {\n    y = rect.y + rect.height / 2;\n  }\n\n  // Left & Right\n  if (leftRight === 'l') {\n    x = rect.x;\n  } else if (leftRight === 'r') {\n    x = rect.x + rect.width;\n  } else {\n    x = rect.x + rect.width / 2;\n  }\n  return {\n    x: x,\n    y: y\n  };\n}\nfunction reversePoints(points, index) {\n  var reverseMap = {\n    t: 'b',\n    b: 't',\n    l: 'r',\n    r: 'l'\n  };\n  return points.map(function (point, i) {\n    if (i === index) {\n      return reverseMap[point] || 'c';\n    }\n    return point;\n  }).join('');\n}\nexport default function useAlign(open, popupEle, target, placement, builtinPlacements, popupAlign, onPopupAlign) {\n  var _React$useState = React.useState({\n      ready: false,\n      offsetX: 0,\n      offsetY: 0,\n      offsetR: 0,\n      offsetB: 0,\n      arrowX: 0,\n      arrowY: 0,\n      scaleX: 1,\n      scaleY: 1,\n      align: builtinPlacements[placement] || {}\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    offsetInfo = _React$useState2[0],\n    setOffsetInfo = _React$useState2[1];\n  var alignCountRef = React.useRef(0);\n  var scrollerList = React.useMemo(function () {\n    if (!popupEle) {\n      return [];\n    }\n    return collectScroller(popupEle);\n  }, [popupEle]);\n\n  // ========================= Flip ==========================\n  // We will memo flip info.\n  // If size change to make flip, it will memo the flip info and use it in next align.\n  var prevFlipRef = React.useRef({});\n  var resetFlipCache = function resetFlipCache() {\n    prevFlipRef.current = {};\n  };\n  if (!open) {\n    resetFlipCache();\n  }\n\n  // ========================= Align =========================\n  var onAlign = useEvent(function () {\n    if (popupEle && target && open) {\n      var _popupElement$parentE, _popupRect$x, _popupRect$y, _popupElement$parentE2;\n      var popupElement = popupEle;\n      var doc = popupElement.ownerDocument;\n      var win = getWin(popupElement);\n      var _win$getComputedStyle = win.getComputedStyle(popupElement),\n        popupPosition = _win$getComputedStyle.position;\n      var originLeft = popupElement.style.left;\n      var originTop = popupElement.style.top;\n      var originRight = popupElement.style.right;\n      var originBottom = popupElement.style.bottom;\n      var originOverflow = popupElement.style.overflow;\n\n      // Placement\n      var placementInfo = _objectSpread(_objectSpread({}, builtinPlacements[placement]), popupAlign);\n\n      // placeholder element\n      var placeholderElement = doc.createElement('div');\n      (_popupElement$parentE = popupElement.parentElement) === null || _popupElement$parentE === void 0 || _popupElement$parentE.appendChild(placeholderElement);\n      placeholderElement.style.left = \"\".concat(popupElement.offsetLeft, \"px\");\n      placeholderElement.style.top = \"\".concat(popupElement.offsetTop, \"px\");\n      placeholderElement.style.position = popupPosition;\n      placeholderElement.style.height = \"\".concat(popupElement.offsetHeight, \"px\");\n      placeholderElement.style.width = \"\".concat(popupElement.offsetWidth, \"px\");\n\n      // Reset first\n      popupElement.style.left = '0';\n      popupElement.style.top = '0';\n      popupElement.style.right = 'auto';\n      popupElement.style.bottom = 'auto';\n      popupElement.style.overflow = 'hidden';\n\n      // Calculate align style, we should consider `transform` case\n      var targetRect;\n      if (Array.isArray(target)) {\n        targetRect = {\n          x: target[0],\n          y: target[1],\n          width: 0,\n          height: 0\n        };\n      } else {\n        var _rect$x, _rect$y;\n        var rect = target.getBoundingClientRect();\n        rect.x = (_rect$x = rect.x) !== null && _rect$x !== void 0 ? _rect$x : rect.left;\n        rect.y = (_rect$y = rect.y) !== null && _rect$y !== void 0 ? _rect$y : rect.top;\n        targetRect = {\n          x: rect.x,\n          y: rect.y,\n          width: rect.width,\n          height: rect.height\n        };\n      }\n      var popupRect = popupElement.getBoundingClientRect();\n      var _win$getComputedStyle2 = win.getComputedStyle(popupElement),\n        height = _win$getComputedStyle2.height,\n        width = _win$getComputedStyle2.width;\n      popupRect.x = (_popupRect$x = popupRect.x) !== null && _popupRect$x !== void 0 ? _popupRect$x : popupRect.left;\n      popupRect.y = (_popupRect$y = popupRect.y) !== null && _popupRect$y !== void 0 ? _popupRect$y : popupRect.top;\n      var _doc$documentElement = doc.documentElement,\n        clientWidth = _doc$documentElement.clientWidth,\n        clientHeight = _doc$documentElement.clientHeight,\n        scrollWidth = _doc$documentElement.scrollWidth,\n        scrollHeight = _doc$documentElement.scrollHeight,\n        scrollTop = _doc$documentElement.scrollTop,\n        scrollLeft = _doc$documentElement.scrollLeft;\n      var popupHeight = popupRect.height;\n      var popupWidth = popupRect.width;\n      var targetHeight = targetRect.height;\n      var targetWidth = targetRect.width;\n\n      // Get bounding of visible area\n      var visibleRegion = {\n        left: 0,\n        top: 0,\n        right: clientWidth,\n        bottom: clientHeight\n      };\n      var scrollRegion = {\n        left: -scrollLeft,\n        top: -scrollTop,\n        right: scrollWidth - scrollLeft,\n        bottom: scrollHeight - scrollTop\n      };\n      var htmlRegion = placementInfo.htmlRegion;\n      var VISIBLE = 'visible';\n      var VISIBLE_FIRST = 'visibleFirst';\n      if (htmlRegion !== 'scroll' && htmlRegion !== VISIBLE_FIRST) {\n        htmlRegion = VISIBLE;\n      }\n      var isVisibleFirst = htmlRegion === VISIBLE_FIRST;\n      var scrollRegionArea = getVisibleArea(scrollRegion, scrollerList);\n      var visibleRegionArea = getVisibleArea(visibleRegion, scrollerList);\n      var visibleArea = htmlRegion === VISIBLE ? visibleRegionArea : scrollRegionArea;\n\n      // When set to `visibleFirst`,\n      // the check `adjust` logic will use `visibleRegion` for check first.\n      var adjustCheckVisibleArea = isVisibleFirst ? visibleRegionArea : visibleArea;\n\n      // Record right & bottom align data\n      popupElement.style.left = 'auto';\n      popupElement.style.top = 'auto';\n      popupElement.style.right = '0';\n      popupElement.style.bottom = '0';\n      var popupMirrorRect = popupElement.getBoundingClientRect();\n\n      // Reset back\n      popupElement.style.left = originLeft;\n      popupElement.style.top = originTop;\n      popupElement.style.right = originRight;\n      popupElement.style.bottom = originBottom;\n      popupElement.style.overflow = originOverflow;\n      (_popupElement$parentE2 = popupElement.parentElement) === null || _popupElement$parentE2 === void 0 || _popupElement$parentE2.removeChild(placeholderElement);\n\n      // Calculate scale\n      var _scaleX = toNum(Math.round(popupWidth / parseFloat(width) * 1000) / 1000);\n      var _scaleY = toNum(Math.round(popupHeight / parseFloat(height) * 1000) / 1000);\n\n      // No need to align since it's not visible in view\n      if (_scaleX === 0 || _scaleY === 0 || isDOM(target) && !isVisible(target)) {\n        return;\n      }\n\n      // Offset\n      var offset = placementInfo.offset,\n        targetOffset = placementInfo.targetOffset;\n      var _getNumberOffset = getNumberOffset(popupRect, offset),\n        _getNumberOffset2 = _slicedToArray(_getNumberOffset, 2),\n        popupOffsetX = _getNumberOffset2[0],\n        popupOffsetY = _getNumberOffset2[1];\n      var _getNumberOffset3 = getNumberOffset(targetRect, targetOffset),\n        _getNumberOffset4 = _slicedToArray(_getNumberOffset3, 2),\n        targetOffsetX = _getNumberOffset4[0],\n        targetOffsetY = _getNumberOffset4[1];\n      targetRect.x -= targetOffsetX;\n      targetRect.y -= targetOffsetY;\n\n      // Points\n      var _ref3 = placementInfo.points || [],\n        _ref4 = _slicedToArray(_ref3, 2),\n        popupPoint = _ref4[0],\n        targetPoint = _ref4[1];\n      var targetPoints = splitPoints(targetPoint);\n      var popupPoints = splitPoints(popupPoint);\n      var targetAlignPoint = getAlignPoint(targetRect, targetPoints);\n      var popupAlignPoint = getAlignPoint(popupRect, popupPoints);\n\n      // Real align info may not same as origin one\n      var nextAlignInfo = _objectSpread({}, placementInfo);\n\n      // Next Offset\n      var nextOffsetX = targetAlignPoint.x - popupAlignPoint.x + popupOffsetX;\n      var nextOffsetY = targetAlignPoint.y - popupAlignPoint.y + popupOffsetY;\n\n      // ============== Intersection ===============\n      // Get area by position. Used for check if flip area is better\n      function getIntersectionVisibleArea(offsetX, offsetY) {\n        var area = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : visibleArea;\n        var l = popupRect.x + offsetX;\n        var t = popupRect.y + offsetY;\n        var r = l + popupWidth;\n        var b = t + popupHeight;\n        var visibleL = Math.max(l, area.left);\n        var visibleT = Math.max(t, area.top);\n        var visibleR = Math.min(r, area.right);\n        var visibleB = Math.min(b, area.bottom);\n        return Math.max(0, (visibleR - visibleL) * (visibleB - visibleT));\n      }\n      var originIntersectionVisibleArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY);\n\n      // As `visibleFirst`, we prepare this for check\n      var originIntersectionRecommendArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY, visibleRegionArea);\n\n      // ========================== Overflow ===========================\n      var targetAlignPointTL = getAlignPoint(targetRect, ['t', 'l']);\n      var popupAlignPointTL = getAlignPoint(popupRect, ['t', 'l']);\n      var targetAlignPointBR = getAlignPoint(targetRect, ['b', 'r']);\n      var popupAlignPointBR = getAlignPoint(popupRect, ['b', 'r']);\n      var overflow = placementInfo.overflow || {};\n      var adjustX = overflow.adjustX,\n        adjustY = overflow.adjustY,\n        shiftX = overflow.shiftX,\n        shiftY = overflow.shiftY;\n      var supportAdjust = function supportAdjust(val) {\n        if (typeof val === 'boolean') {\n          return val;\n        }\n        return val >= 0;\n      };\n\n      // Prepare position\n      var nextPopupY;\n      var nextPopupBottom;\n      var nextPopupX;\n      var nextPopupRight;\n      function syncNextPopupPosition() {\n        nextPopupY = popupRect.y + nextOffsetY;\n        nextPopupBottom = nextPopupY + popupHeight;\n        nextPopupX = popupRect.x + nextOffsetX;\n        nextPopupRight = nextPopupX + popupWidth;\n      }\n      syncNextPopupPosition();\n\n      // >>>>>>>>>> Top & Bottom\n      var needAdjustY = supportAdjust(adjustY);\n      var sameTB = popupPoints[0] === targetPoints[0];\n\n      // Bottom to Top\n      if (needAdjustY && popupPoints[0] === 't' && (nextPopupBottom > adjustCheckVisibleArea.bottom || prevFlipRef.current.bt)) {\n        var tmpNextOffsetY = nextOffsetY;\n        if (sameTB) {\n          tmpNextOffsetY -= popupHeight - targetHeight;\n        } else {\n          tmpNextOffsetY = targetAlignPointTL.y - popupAlignPointBR.y - popupOffsetY;\n        }\n        var newVisibleArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY);\n        var newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        newVisibleArea > originIntersectionVisibleArea || newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        newVisibleRecommendArea >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.bt = true;\n          nextOffsetY = tmpNextOffsetY;\n          popupOffsetY = -popupOffsetY;\n          nextAlignInfo.points = [reversePoints(popupPoints, 0), reversePoints(targetPoints, 0)];\n        } else {\n          prevFlipRef.current.bt = false;\n        }\n      }\n\n      // Top to Bottom\n      if (needAdjustY && popupPoints[0] === 'b' && (nextPopupY < adjustCheckVisibleArea.top || prevFlipRef.current.tb)) {\n        var _tmpNextOffsetY = nextOffsetY;\n        if (sameTB) {\n          _tmpNextOffsetY += popupHeight - targetHeight;\n        } else {\n          _tmpNextOffsetY = targetAlignPointBR.y - popupAlignPointTL.y - popupOffsetY;\n        }\n        var _newVisibleArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY);\n        var _newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea > originIntersectionVisibleArea || _newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.tb = true;\n          nextOffsetY = _tmpNextOffsetY;\n          popupOffsetY = -popupOffsetY;\n          nextAlignInfo.points = [reversePoints(popupPoints, 0), reversePoints(targetPoints, 0)];\n        } else {\n          prevFlipRef.current.tb = false;\n        }\n      }\n\n      // >>>>>>>>>> Left & Right\n      var needAdjustX = supportAdjust(adjustX);\n\n      // >>>>> Flip\n      var sameLR = popupPoints[1] === targetPoints[1];\n\n      // Right to Left\n      if (needAdjustX && popupPoints[1] === 'l' && (nextPopupRight > adjustCheckVisibleArea.right || prevFlipRef.current.rl)) {\n        var tmpNextOffsetX = nextOffsetX;\n        if (sameLR) {\n          tmpNextOffsetX -= popupWidth - targetWidth;\n        } else {\n          tmpNextOffsetX = targetAlignPointTL.x - popupAlignPointBR.x - popupOffsetX;\n        }\n        var _newVisibleArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY);\n        var _newVisibleRecommendArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea2 > originIntersectionVisibleArea || _newVisibleArea2 === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea2 >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.rl = true;\n          nextOffsetX = tmpNextOffsetX;\n          popupOffsetX = -popupOffsetX;\n          nextAlignInfo.points = [reversePoints(popupPoints, 1), reversePoints(targetPoints, 1)];\n        } else {\n          prevFlipRef.current.rl = false;\n        }\n      }\n\n      // Left to Right\n      if (needAdjustX && popupPoints[1] === 'r' && (nextPopupX < adjustCheckVisibleArea.left || prevFlipRef.current.lr)) {\n        var _tmpNextOffsetX = nextOffsetX;\n        if (sameLR) {\n          _tmpNextOffsetX += popupWidth - targetWidth;\n        } else {\n          _tmpNextOffsetX = targetAlignPointBR.x - popupAlignPointTL.x - popupOffsetX;\n        }\n        var _newVisibleArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY);\n        var _newVisibleRecommendArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea3 > originIntersectionVisibleArea || _newVisibleArea3 === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea3 >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.lr = true;\n          nextOffsetX = _tmpNextOffsetX;\n          popupOffsetX = -popupOffsetX;\n          nextAlignInfo.points = [reversePoints(popupPoints, 1), reversePoints(targetPoints, 1)];\n        } else {\n          prevFlipRef.current.lr = false;\n        }\n      }\n\n      // ============================ Shift ============================\n      syncNextPopupPosition();\n      var numShiftX = shiftX === true ? 0 : shiftX;\n      if (typeof numShiftX === 'number') {\n        // Left\n        if (nextPopupX < visibleRegionArea.left) {\n          nextOffsetX -= nextPopupX - visibleRegionArea.left - popupOffsetX;\n          if (targetRect.x + targetWidth < visibleRegionArea.left + numShiftX) {\n            nextOffsetX += targetRect.x - visibleRegionArea.left + targetWidth - numShiftX;\n          }\n        }\n\n        // Right\n        if (nextPopupRight > visibleRegionArea.right) {\n          nextOffsetX -= nextPopupRight - visibleRegionArea.right - popupOffsetX;\n          if (targetRect.x > visibleRegionArea.right - numShiftX) {\n            nextOffsetX += targetRect.x - visibleRegionArea.right + numShiftX;\n          }\n        }\n      }\n      var numShiftY = shiftY === true ? 0 : shiftY;\n      if (typeof numShiftY === 'number') {\n        // Top\n        if (nextPopupY < visibleRegionArea.top) {\n          nextOffsetY -= nextPopupY - visibleRegionArea.top - popupOffsetY;\n\n          // When target if far away from visible area\n          // Stop shift\n          if (targetRect.y + targetHeight < visibleRegionArea.top + numShiftY) {\n            nextOffsetY += targetRect.y - visibleRegionArea.top + targetHeight - numShiftY;\n          }\n        }\n\n        // Bottom\n        if (nextPopupBottom > visibleRegionArea.bottom) {\n          nextOffsetY -= nextPopupBottom - visibleRegionArea.bottom - popupOffsetY;\n          if (targetRect.y > visibleRegionArea.bottom - numShiftY) {\n            nextOffsetY += targetRect.y - visibleRegionArea.bottom + numShiftY;\n          }\n        }\n      }\n\n      // ============================ Arrow ============================\n      // Arrow center align\n      var popupLeft = popupRect.x + nextOffsetX;\n      var popupRight = popupLeft + popupWidth;\n      var popupTop = popupRect.y + nextOffsetY;\n      var popupBottom = popupTop + popupHeight;\n      var targetLeft = targetRect.x;\n      var targetRight = targetLeft + targetWidth;\n      var targetTop = targetRect.y;\n      var targetBottom = targetTop + targetHeight;\n      var maxLeft = Math.max(popupLeft, targetLeft);\n      var minRight = Math.min(popupRight, targetRight);\n      var xCenter = (maxLeft + minRight) / 2;\n      var nextArrowX = xCenter - popupLeft;\n      var maxTop = Math.max(popupTop, targetTop);\n      var minBottom = Math.min(popupBottom, targetBottom);\n      var yCenter = (maxTop + minBottom) / 2;\n      var nextArrowY = yCenter - popupTop;\n      onPopupAlign === null || onPopupAlign === void 0 || onPopupAlign(popupEle, nextAlignInfo);\n\n      // Additional calculate right & bottom position\n      var offsetX4Right = popupMirrorRect.right - popupRect.x - (nextOffsetX + popupRect.width);\n      var offsetY4Bottom = popupMirrorRect.bottom - popupRect.y - (nextOffsetY + popupRect.height);\n      if (_scaleX === 1) {\n        nextOffsetX = Math.round(nextOffsetX);\n        offsetX4Right = Math.round(offsetX4Right);\n      }\n      if (_scaleY === 1) {\n        nextOffsetY = Math.round(nextOffsetY);\n        offsetY4Bottom = Math.round(offsetY4Bottom);\n      }\n      var nextOffsetInfo = {\n        ready: true,\n        offsetX: nextOffsetX / _scaleX,\n        offsetY: nextOffsetY / _scaleY,\n        offsetR: offsetX4Right / _scaleX,\n        offsetB: offsetY4Bottom / _scaleY,\n        arrowX: nextArrowX / _scaleX,\n        arrowY: nextArrowY / _scaleY,\n        scaleX: _scaleX,\n        scaleY: _scaleY,\n        align: nextAlignInfo\n      };\n      setOffsetInfo(nextOffsetInfo);\n    }\n  });\n  var triggerAlign = function triggerAlign() {\n    alignCountRef.current += 1;\n    var id = alignCountRef.current;\n\n    // Merge all align requirement into one frame\n    Promise.resolve().then(function () {\n      if (alignCountRef.current === id) {\n        onAlign();\n      }\n    });\n  };\n\n  // Reset ready status when placement & open changed\n  var resetReady = function resetReady() {\n    setOffsetInfo(function (ori) {\n      return _objectSpread(_objectSpread({}, ori), {}, {\n        ready: false\n      });\n    });\n  };\n  useLayoutEffect(resetReady, [placement]);\n  useLayoutEffect(function () {\n    if (!open) {\n      resetReady();\n    }\n  }, [open]);\n  return [offsetInfo.ready, offsetInfo.offsetX, offsetInfo.offsetY, offsetInfo.offsetR, offsetInfo.offsetB, offsetInfo.arrowX, offsetInfo.arrowY, offsetInfo.scaleX, offsetInfo.scaleY, offsetInfo.align, triggerAlign];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}