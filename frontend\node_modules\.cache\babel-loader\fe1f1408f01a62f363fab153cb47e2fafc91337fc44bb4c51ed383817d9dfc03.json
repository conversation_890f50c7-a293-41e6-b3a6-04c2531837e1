{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar _excluded = [\"className\", \"style\", \"classNames\", \"styles\"];\nimport React, { useContext, useEffect, useRef, useState } from 'react';\nimport clsx from 'classnames';\nimport { CSSMotionList } from 'rc-motion';\nimport Notice from \"./Notice\";\nimport { NotificationContext } from \"./NotificationProvider\";\nimport useStack from \"./hooks/useStack\";\nvar NoticeList = function NoticeList(props) {\n  var configList = props.configList,\n    placement = props.placement,\n    prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    motion = props.motion,\n    onAllNoticeRemoved = props.onAllNoticeRemoved,\n    onNoticeClose = props.onNoticeClose,\n    stackConfig = props.stack;\n  var _useContext = useContext(NotificationContext),\n    ctxCls = _useContext.classNames;\n  var dictRef = useRef({});\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    latestNotice = _useState2[0],\n    setLatestNotice = _useState2[1];\n  var _useState3 = useState([]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    hoverKeys = _useState4[0],\n    setHoverKeys = _useState4[1];\n  var keys = configList.map(function (config) {\n    return {\n      config: config,\n      key: String(config.key)\n    };\n  });\n  var _useStack = useStack(stackConfig),\n    _useStack2 = _slicedToArray(_useStack, 2),\n    stack = _useStack2[0],\n    _useStack2$ = _useStack2[1],\n    offset = _useStack2$.offset,\n    threshold = _useStack2$.threshold,\n    gap = _useStack2$.gap;\n  var expanded = stack && (hoverKeys.length > 0 || keys.length <= threshold);\n  var placementMotion = typeof motion === 'function' ? motion(placement) : motion;\n\n  // Clean hover key\n  useEffect(function () {\n    if (stack && hoverKeys.length > 1) {\n      setHoverKeys(function (prev) {\n        return prev.filter(function (key) {\n          return keys.some(function (_ref) {\n            var dataKey = _ref.key;\n            return key === dataKey;\n          });\n        });\n      });\n    }\n  }, [hoverKeys, keys, stack]);\n\n  // Force update latest notice\n  useEffect(function () {\n    var _keys;\n    if (stack && dictRef.current[(_keys = keys[keys.length - 1]) === null || _keys === void 0 ? void 0 : _keys.key]) {\n      var _keys2;\n      setLatestNotice(dictRef.current[(_keys2 = keys[keys.length - 1]) === null || _keys2 === void 0 ? void 0 : _keys2.key]);\n    }\n  }, [keys, stack]);\n  return /*#__PURE__*/React.createElement(CSSMotionList, _extends({\n    key: placement,\n    className: clsx(prefixCls, \"\".concat(prefixCls, \"-\").concat(placement), ctxCls === null || ctxCls === void 0 ? void 0 : ctxCls.list, className, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-stack\"), !!stack), \"\".concat(prefixCls, \"-stack-expanded\"), expanded)),\n    style: style,\n    keys: keys,\n    motionAppear: true\n  }, placementMotion, {\n    onAllRemoved: function onAllRemoved() {\n      onAllNoticeRemoved(placement);\n    }\n  }), function (_ref2, nodeRef) {\n    var config = _ref2.config,\n      motionClassName = _ref2.className,\n      motionStyle = _ref2.style,\n      motionIndex = _ref2.index;\n    var _ref3 = config,\n      key = _ref3.key,\n      times = _ref3.times;\n    var strKey = String(key);\n    var _ref4 = config,\n      configClassName = _ref4.className,\n      configStyle = _ref4.style,\n      configClassNames = _ref4.classNames,\n      configStyles = _ref4.styles,\n      restConfig = _objectWithoutProperties(_ref4, _excluded);\n    var dataIndex = keys.findIndex(function (item) {\n      return item.key === strKey;\n    });\n\n    // If dataIndex is -1, that means this notice has been removed in data, but still in dom\n    // Should minus (motionIndex - 1) to get the correct index because keys.length is not the same as dom length\n    var stackStyle = {};\n    if (stack) {\n      var index = keys.length - 1 - (dataIndex > -1 ? dataIndex : motionIndex - 1);\n      var transformX = placement === 'top' || placement === 'bottom' ? '-50%' : '0';\n      if (index > 0) {\n        var _dictRef$current$strK, _dictRef$current$strK2, _dictRef$current$strK3;\n        stackStyle.height = expanded ? (_dictRef$current$strK = dictRef.current[strKey]) === null || _dictRef$current$strK === void 0 ? void 0 : _dictRef$current$strK.offsetHeight : latestNotice === null || latestNotice === void 0 ? void 0 : latestNotice.offsetHeight;\n\n        // Transform\n        var verticalOffset = 0;\n        for (var i = 0; i < index; i++) {\n          var _dictRef$current$keys;\n          verticalOffset += ((_dictRef$current$keys = dictRef.current[keys[keys.length - 1 - i].key]) === null || _dictRef$current$keys === void 0 ? void 0 : _dictRef$current$keys.offsetHeight) + gap;\n        }\n        var transformY = (expanded ? verticalOffset : index * offset) * (placement.startsWith('top') ? 1 : -1);\n        var scaleX = !expanded && latestNotice !== null && latestNotice !== void 0 && latestNotice.offsetWidth && (_dictRef$current$strK2 = dictRef.current[strKey]) !== null && _dictRef$current$strK2 !== void 0 && _dictRef$current$strK2.offsetWidth ? ((latestNotice === null || latestNotice === void 0 ? void 0 : latestNotice.offsetWidth) - offset * 2 * (index < 3 ? index : 3)) / ((_dictRef$current$strK3 = dictRef.current[strKey]) === null || _dictRef$current$strK3 === void 0 ? void 0 : _dictRef$current$strK3.offsetWidth) : 1;\n        stackStyle.transform = \"translate3d(\".concat(transformX, \", \").concat(transformY, \"px, 0) scaleX(\").concat(scaleX, \")\");\n      } else {\n        stackStyle.transform = \"translate3d(\".concat(transformX, \", 0, 0)\");\n      }\n    }\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: nodeRef,\n      className: clsx(\"\".concat(prefixCls, \"-notice-wrapper\"), motionClassName, configClassNames === null || configClassNames === void 0 ? void 0 : configClassNames.wrapper),\n      style: _objectSpread(_objectSpread(_objectSpread({}, motionStyle), stackStyle), configStyles === null || configStyles === void 0 ? void 0 : configStyles.wrapper),\n      onMouseEnter: function onMouseEnter() {\n        return setHoverKeys(function (prev) {\n          return prev.includes(strKey) ? prev : [].concat(_toConsumableArray(prev), [strKey]);\n        });\n      },\n      onMouseLeave: function onMouseLeave() {\n        return setHoverKeys(function (prev) {\n          return prev.filter(function (k) {\n            return k !== strKey;\n          });\n        });\n      }\n    }, /*#__PURE__*/React.createElement(Notice, _extends({}, restConfig, {\n      ref: function ref(node) {\n        if (dataIndex > -1) {\n          dictRef.current[strKey] = node;\n        } else {\n          delete dictRef.current[strKey];\n        }\n      },\n      prefixCls: prefixCls,\n      classNames: configClassNames,\n      styles: configStyles,\n      className: clsx(configClassName, ctxCls === null || ctxCls === void 0 ? void 0 : ctxCls.notice),\n      style: configStyle,\n      times: times,\n      key: key,\n      eventKey: key,\n      onNoticeClose: onNoticeClose,\n      hovering: stack && hoverKeys.length > 0\n    })));\n  });\n};\nif (process.env.NODE_ENV !== 'production') {\n  NoticeList.displayName = 'NoticeList';\n}\nexport default NoticeList;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}