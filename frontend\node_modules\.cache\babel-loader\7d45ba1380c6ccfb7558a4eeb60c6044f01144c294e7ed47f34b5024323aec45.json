{"ast": null, "code": "var _jsxFileName = \"D:\\\\study\\\\college\\\\junior2\\\\dataBase\\\\system2\\\\frontend\\\\src\\\\components\\\\Layout\\\\AppHeader.js\";\n/**\n * 应用头部组件\n * 实现苹果风格的导航栏\n */\n\nimport React from 'react';\nimport { Layout, Typography, Avatar, Dropdown } from 'antd';\nimport { UserOutlined, SettingOutlined, LogoutOutlined } from '@ant-design/icons';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header\n} = Layout;\nconst {\n  Title\n} = Typography;\n\n// 样式化头部\nconst StyledHeader = styled(Header)`\n  background: rgba(255, 255, 255, 0.15);\n  backdrop-filter: blur(20px);\n  -webkit-backdrop-filter: blur(20px);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.18);\n  padding: 0 24px;\n  height: 64px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n`;\n_c = StyledHeader;\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\n_c2 = Logo;\nconst LogoIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #007AFF, #5856D6);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 20px;\n  font-weight: bold;\n  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);\n`;\n_c3 = LogoIcon;\nconst AppTitle = styled(Title)`\n  &.ant-typography {\n    color: white !important;\n    margin: 0 !important;\n    font-size: 20px !important;\n    font-weight: 600 !important;\n  }\n`;\n_c4 = AppTitle;\nconst UserSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 16px;\n`;\n_c5 = UserSection;\nconst UserInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  \n  .username {\n    color: white;\n    font-weight: 500;\n    font-size: 14px;\n    line-height: 1.2;\n  }\n  \n  .role {\n    color: rgba(255, 255, 255, 0.7);\n    font-size: 12px;\n    line-height: 1.2;\n  }\n`;\n_c6 = UserInfo;\nconst StyledAvatar = styled(Avatar)`\n  background: linear-gradient(135deg, #34C759, #30B94D);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: scale(1.05);\n    border-color: rgba(255, 255, 255, 0.5);\n  }\n`;\n_c7 = StyledAvatar;\nfunction AppHeader() {\n  // 用户菜单项\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 13\n    }, this),\n    label: '个人资料'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this),\n    label: '设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this),\n    label: '退出登录',\n    danger: true\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    switch (key) {\n      case 'profile':\n        // 跳转到个人资料页面\n        break;\n      case 'settings':\n        // 打开设置页面\n        break;\n      case 'logout':\n        // 处理退出登录\n        break;\n      default:\n        break;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(StyledHeader, {\n    children: [/*#__PURE__*/_jsxDEV(Logo, {\n      children: [/*#__PURE__*/_jsxDEV(LogoIcon, {\n        children: \"H\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AppTitle, {\n        level: 4,\n        children: \"\\u667A\\u80FD\\u5065\\u5EB7\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(UserSection, {\n      children: [/*#__PURE__*/_jsxDEV(UserInfo, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"username\",\n          children: \"\\u5065\\u5EB7\\u7528\\u6237\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"role\",\n          children: \"\\u4E2A\\u4EBA\\u7248\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n        menu: {\n          items: userMenuItems,\n          onClick: handleMenuClick\n        },\n        placement: \"bottomRight\",\n        arrow: true,\n        children: /*#__PURE__*/_jsxDEV(StyledAvatar, {\n          size: 40,\n          icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 5\n  }, this);\n}\n_c8 = AppHeader;\nexport default AppHeader;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"StyledHeader\");\n$RefreshReg$(_c2, \"Logo\");\n$RefreshReg$(_c3, \"LogoIcon\");\n$RefreshReg$(_c4, \"AppTitle\");\n$RefreshReg$(_c5, \"UserSection\");\n$RefreshReg$(_c6, \"UserInfo\");\n$RefreshReg$(_c7, \"StyledAvatar\");\n$RefreshReg$(_c8, \"AppHeader\");", "map": {"version": 3, "names": ["React", "Layout", "Typography", "Avatar", "Dropdown", "UserOutlined", "SettingOutlined", "LogoutOutlined", "styled", "jsxDEV", "_jsxDEV", "Header", "Title", "StyledHeader", "_c", "Logo", "div", "_c2", "LogoIcon", "_c3", "AppTitle", "_c4", "UserSection", "_c5", "UserInfo", "_c6", "Styled<PERSON>vatar", "_c7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "userMenuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "type", "danger", "handleMenuClick", "children", "level", "className", "menu", "items", "onClick", "placement", "arrow", "size", "_c8", "$RefreshReg$"], "sources": ["D:/study/college/junior2/dataBase/system2/frontend/src/components/Layout/AppHeader.js"], "sourcesContent": ["/**\n * 应用头部组件\n * 实现苹果风格的导航栏\n */\n\nimport React from 'react';\nimport { Layout, Typography, Avatar, Dropdown } from 'antd';\nimport { UserOutlined, SettingOutlined, LogoutOutlined } from '@ant-design/icons';\nimport styled from 'styled-components';\n\nconst { Header } = Layout;\nconst { Title } = Typography;\n\n// 样式化头部\nconst StyledHeader = styled(Header)`\n  background: rgba(255, 255, 255, 0.15);\n  backdrop-filter: blur(20px);\n  -webkit-backdrop-filter: blur(20px);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.18);\n  padding: 0 24px;\n  height: 64px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n`;\n\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\n\nconst LogoIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #007AFF, #5856D6);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 20px;\n  font-weight: bold;\n  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);\n`;\n\nconst AppTitle = styled(Title)`\n  &.ant-typography {\n    color: white !important;\n    margin: 0 !important;\n    font-size: 20px !important;\n    font-weight: 600 !important;\n  }\n`;\n\nconst UserSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 16px;\n`;\n\nconst UserInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  \n  .username {\n    color: white;\n    font-weight: 500;\n    font-size: 14px;\n    line-height: 1.2;\n  }\n  \n  .role {\n    color: rgba(255, 255, 255, 0.7);\n    font-size: 12px;\n    line-height: 1.2;\n  }\n`;\n\nconst StyledAvatar = styled(Avatar)`\n  background: linear-gradient(135deg, #34C759, #30B94D);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: scale(1.05);\n    border-color: rgba(255, 255, 255, 0.5);\n  }\n`;\n\nfunction AppHeader() {\n  // 用户菜单项\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '设置',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      danger: true,\n    },\n  ];\n\n  const handleMenuClick = ({ key }) => {\n    switch (key) {\n      case 'profile':\n        // 跳转到个人资料页面\n        break;\n      case 'settings':\n        // 打开设置页面\n        break;\n      case 'logout':\n        // 处理退出登录\n        break;\n      default:\n        break;\n    }\n  };\n\n  return (\n    <StyledHeader>\n      <Logo>\n        <LogoIcon>H</LogoIcon>\n        <AppTitle level={4}>智能健康管理系统</AppTitle>\n      </Logo>\n      \n      <UserSection>\n        <UserInfo>\n          <div className=\"username\">健康用户</div>\n          <div className=\"role\">个人版</div>\n        </UserInfo>\n        \n        <Dropdown\n          menu={{\n            items: userMenuItems,\n            onClick: handleMenuClick,\n          }}\n          placement=\"bottomRight\"\n          arrow\n        >\n          <StyledAvatar size={40} icon={<UserOutlined />} />\n        </Dropdown>\n      </UserSection>\n    </StyledHeader>\n  );\n}\n\nexport default AppHeader;\n"], "mappings": ";AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,MAAM;AAC3D,SAASC,YAAY,EAAEC,eAAe,EAAEC,cAAc,QAAQ,mBAAmB;AACjF,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAM;EAAEC;AAAO,CAAC,GAAGV,MAAM;AACzB,MAAM;EAAEW;AAAM,CAAC,GAAGV,UAAU;;AAE5B;AACA,MAAMW,YAAY,GAAGL,MAAM,CAACG,MAAM,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,EAAA,GAbID,YAAY;AAelB,MAAME,IAAI,GAAGP,MAAM,CAACQ,GAAG;AACvB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,IAAI;AAMV,MAAMG,QAAQ,GAAGV,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAZID,QAAQ;AAcd,MAAME,QAAQ,GAAGZ,MAAM,CAACI,KAAK,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACS,GAAA,GAPID,QAAQ;AASd,MAAME,WAAW,GAAGd,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,WAAW;AAMjB,MAAME,QAAQ,GAAGhB,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACS,GAAA,GAjBID,QAAQ;AAmBd,MAAME,YAAY,GAAGlB,MAAM,CAACL,MAAM,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GAVID,YAAY;AAYlB,SAASE,SAASA,CAAA,EAAG;EACnB;EACA,MAAMC,aAAa,GAAG,CACpB;IACEC,GAAG,EAAE,SAAS;IACdC,IAAI,eAAErB,OAAA,CAACL,YAAY;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAErB,OAAA,CAACJ,eAAe;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAErB,OAAA,CAACH,cAAc;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,eAAe,GAAGA,CAAC;IAAET;EAAI,CAAC,KAAK;IACnC,QAAQA,GAAG;MACT,KAAK,SAAS;QACZ;QACA;MACF,KAAK,UAAU;QACb;QACA;MACF,KAAK,QAAQ;QACX;QACA;MACF;QACE;IACJ;EACF,CAAC;EAED,oBACEpB,OAAA,CAACG,YAAY;IAAA2B,QAAA,gBACX9B,OAAA,CAACK,IAAI;MAAAyB,QAAA,gBACH9B,OAAA,CAACQ,QAAQ;QAAAsB,QAAA,EAAC;MAAC;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACtBzB,OAAA,CAACU,QAAQ;QAACqB,KAAK,EAAE,CAAE;QAAAD,QAAA,EAAC;MAAQ;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eAEPzB,OAAA,CAACY,WAAW;MAAAkB,QAAA,gBACV9B,OAAA,CAACc,QAAQ;QAAAgB,QAAA,gBACP9B,OAAA;UAAKgC,SAAS,EAAC,UAAU;UAAAF,QAAA,EAAC;QAAI;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpCzB,OAAA;UAAKgC,SAAS,EAAC,MAAM;UAAAF,QAAA,EAAC;QAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eAEXzB,OAAA,CAACN,QAAQ;QACPuC,IAAI,EAAE;UACJC,KAAK,EAAEf,aAAa;UACpBgB,OAAO,EAAEN;QACX,CAAE;QACFO,SAAS,EAAC,aAAa;QACvBC,KAAK;QAAAP,QAAA,eAEL9B,OAAA,CAACgB,YAAY;UAACsB,IAAI,EAAE,EAAG;UAACjB,IAAI,eAAErB,OAAA,CAACL,YAAY;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB;AAACc,GAAA,GAlEQrB,SAAS;AAoElB,eAAeA,SAAS;AAAC,IAAAd,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAsB,GAAA;AAAAC,YAAA,CAAApC,EAAA;AAAAoC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}