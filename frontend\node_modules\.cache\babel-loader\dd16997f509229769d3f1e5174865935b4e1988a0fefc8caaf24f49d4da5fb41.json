{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport { useEffect, useRef, useState } from 'react';\nimport getFixScaleEleTransPosition from \"../getFixScaleEleTransPosition\";\nfunction getDistance(a, b) {\n  var x = a.x - b.x;\n  var y = a.y - b.y;\n  return Math.hypot(x, y);\n}\nfunction getCenter(oldPoint1, oldPoint2, newPoint1, newPoint2) {\n  // Calculate the distance each point has moved\n  var distance1 = getDistance(oldPoint1, newPoint1);\n  var distance2 = getDistance(oldPoint2, newPoint2);\n\n  // If both distances are 0, return the original points\n  if (distance1 === 0 && distance2 === 0) {\n    return [oldPoint1.x, oldPoint1.y];\n  }\n\n  // Calculate the ratio of the distances\n  var ratio = distance1 / (distance1 + distance2);\n\n  // Calculate the new center point based on the ratio\n  var x = oldPoint1.x + ratio * (oldPoint2.x - oldPoint1.x);\n  var y = oldPoint1.y + ratio * (oldPoint2.y - oldPoint1.y);\n  return [x, y];\n}\nexport default function useTouchEvent(imgRef, movable, visible, minScale, transform, updateTransform, dispatchZoomChange) {\n  var rotate = transform.rotate,\n    scale = transform.scale,\n    x = transform.x,\n    y = transform.y;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isTouching = _useState2[0],\n    setIsTouching = _useState2[1];\n  var touchPointInfo = useRef({\n    point1: {\n      x: 0,\n      y: 0\n    },\n    point2: {\n      x: 0,\n      y: 0\n    },\n    eventType: 'none'\n  });\n  var updateTouchPointInfo = function updateTouchPointInfo(values) {\n    touchPointInfo.current = _objectSpread(_objectSpread({}, touchPointInfo.current), values);\n  };\n  var onTouchStart = function onTouchStart(event) {\n    if (!movable) return;\n    event.stopPropagation();\n    setIsTouching(true);\n    var _event$touches = event.touches,\n      touches = _event$touches === void 0 ? [] : _event$touches;\n    if (touches.length > 1) {\n      // touch zoom\n      updateTouchPointInfo({\n        point1: {\n          x: touches[0].clientX,\n          y: touches[0].clientY\n        },\n        point2: {\n          x: touches[1].clientX,\n          y: touches[1].clientY\n        },\n        eventType: 'touchZoom'\n      });\n    } else {\n      // touch move\n      updateTouchPointInfo({\n        point1: {\n          x: touches[0].clientX - x,\n          y: touches[0].clientY - y\n        },\n        eventType: 'move'\n      });\n    }\n  };\n  var onTouchMove = function onTouchMove(event) {\n    var _event$touches2 = event.touches,\n      touches = _event$touches2 === void 0 ? [] : _event$touches2;\n    var _touchPointInfo$curre = touchPointInfo.current,\n      point1 = _touchPointInfo$curre.point1,\n      point2 = _touchPointInfo$curre.point2,\n      eventType = _touchPointInfo$curre.eventType;\n    if (touches.length > 1 && eventType === 'touchZoom') {\n      // touch zoom\n      var newPoint1 = {\n        x: touches[0].clientX,\n        y: touches[0].clientY\n      };\n      var newPoint2 = {\n        x: touches[1].clientX,\n        y: touches[1].clientY\n      };\n      var _getCenter = getCenter(point1, point2, newPoint1, newPoint2),\n        _getCenter2 = _slicedToArray(_getCenter, 2),\n        centerX = _getCenter2[0],\n        centerY = _getCenter2[1];\n      var ratio = getDistance(newPoint1, newPoint2) / getDistance(point1, point2);\n      dispatchZoomChange(ratio, 'touchZoom', centerX, centerY, true);\n      updateTouchPointInfo({\n        point1: newPoint1,\n        point2: newPoint2,\n        eventType: 'touchZoom'\n      });\n    } else if (eventType === 'move') {\n      // touch move\n      updateTransform({\n        x: touches[0].clientX - point1.x,\n        y: touches[0].clientY - point1.y\n      }, 'move');\n      updateTouchPointInfo({\n        eventType: 'move'\n      });\n    }\n  };\n  var onTouchEnd = function onTouchEnd() {\n    if (!visible) return;\n    if (isTouching) {\n      setIsTouching(false);\n    }\n    updateTouchPointInfo({\n      eventType: 'none'\n    });\n    if (minScale > scale) {\n      /** When the scaling ratio is less than the minimum scaling ratio, reset the scaling ratio */\n      return updateTransform({\n        x: 0,\n        y: 0,\n        scale: minScale\n      }, 'touchZoom');\n    }\n    var width = imgRef.current.offsetWidth * scale;\n    var height = imgRef.current.offsetHeight * scale;\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    var _imgRef$current$getBo = imgRef.current.getBoundingClientRect(),\n      left = _imgRef$current$getBo.left,\n      top = _imgRef$current$getBo.top;\n    var isRotate = rotate % 180 !== 0;\n    var fixState = getFixScaleEleTransPosition(isRotate ? height : width, isRotate ? width : height, left, top);\n    if (fixState) {\n      updateTransform(_objectSpread({}, fixState), 'dragRebound');\n    }\n  };\n  useEffect(function () {\n    var onTouchMoveListener;\n    if (visible && movable) {\n      onTouchMoveListener = addEventListener(window, 'touchmove', function (e) {\n        return e.preventDefault();\n      }, {\n        passive: false\n      });\n    }\n    return function () {\n      var _onTouchMoveListener;\n      (_onTouchMoveListener = onTouchMoveListener) === null || _onTouchMoveListener === void 0 || _onTouchMoveListener.remove();\n    };\n  }, [visible, movable]);\n  return {\n    isTouching: isTouching,\n    onTouchStart: onTouchStart,\n    onTouchMove: onTouchMove,\n    onTouchEnd: onTouchEnd\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}