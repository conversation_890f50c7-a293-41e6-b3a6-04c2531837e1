/**
 * 应用头部组件
 * 实现苹果风格的导航栏
 */

import React from 'react';
import { Layout, Typography, Avatar, Dropdown } from 'antd';
import { UserOutlined, SettingOutlined, LogoutOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const { Header } = Layout;
const { Title } = Typography;

// 样式化头部
const StyledHeader = styled(Header)`
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.18);
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 1000;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const LogoIcon = styled.div`
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #007AFF, #5856D6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
`;

const AppTitle = styled(Title)`
  &.ant-typography {
    color: white !important;
    margin: 0 !important;
    font-size: 20px !important;
    font-weight: 600 !important;
  }
`;

const UserSection = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  
  .username {
    color: white;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.2;
  }
  
  .role {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    line-height: 1.2;
  }
`;

const StyledAvatar = styled(Avatar)`
  background: linear-gradient(135deg, #34C759, #30B94D);
  border: 2px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.5);
  }
`;

function AppHeader() {
  // 用户菜单项
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      danger: true,
    },
  ];

  const handleMenuClick = ({ key }) => {
    switch (key) {
      case 'profile':
        // 跳转到个人资料页面
        break;
      case 'settings':
        // 打开设置页面
        break;
      case 'logout':
        // 处理退出登录
        break;
      default:
        break;
    }
  };

  return (
    <StyledHeader>
      <Logo>
        <LogoIcon>H</LogoIcon>
        <AppTitle level={4}>智能健康管理系统</AppTitle>
      </Logo>
      
      <UserSection>
        <UserInfo>
          <div className="username">健康用户</div>
          <div className="role">个人版</div>
        </UserInfo>
        
        <Dropdown
          menu={{
            items: userMenuItems,
            onClick: handleMenuClick,
          }}
          placement="bottomRight"
          arrow
        >
          <StyledAvatar size={40} icon={<UserOutlined />} />
        </Dropdown>
      </UserSection>
    </StyledHeader>
  );
}

export default AppHeader;
