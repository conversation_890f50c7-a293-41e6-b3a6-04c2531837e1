{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classnames from 'classnames';\nimport React from 'react';\nvar PanelContent = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    forceRender = props.forceRender,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    isActive = props.isActive,\n    role = props.role,\n    customizeClassNames = props.classNames,\n    styles = props.styles;\n  var _React$useState = React.useState(isActive || forceRender),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    rendered = _React$useState2[0],\n    setRendered = _React$useState2[1];\n  React.useEffect(function () {\n    if (forceRender || isActive) {\n      setRendered(true);\n    }\n  }, [forceRender, isActive]);\n  if (!rendered) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    className: classnames(\"\".concat(prefixCls, \"-content\"), _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-content-active\"), isActive), \"\".concat(prefixCls, \"-content-inactive\"), !isActive), className),\n    style: style,\n    role: role\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classnames(\"\".concat(prefixCls, \"-content-box\"), customizeClassNames === null || customizeClassNames === void 0 ? void 0 : customizeClassNames.body),\n    style: styles === null || styles === void 0 ? void 0 : styles.body\n  }, children));\n});\nPanelContent.displayName = 'PanelContent';\nexport default PanelContent;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}