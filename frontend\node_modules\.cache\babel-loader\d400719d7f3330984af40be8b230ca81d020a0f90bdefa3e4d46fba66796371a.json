{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { SEARCH_MARK } from \"../hooks/useSearchOptions\";\nimport { getFullPathKeys, toPathKey } from \"../utils/commonUtil\";\nexport default (function (ref, options, fieldNames, activeValueCells, setActiveValueCells, onKeyBoardSelect, contextProps) {\n  var direction = contextProps.direction,\n    searchValue = contextProps.searchValue,\n    toggleOpen = contextProps.toggleOpen,\n    open = contextProps.open;\n  var rtl = direction === 'rtl';\n  var _React$useMemo = React.useMemo(function () {\n      var activeIndex = -1;\n      var currentOptions = options;\n      var mergedActiveIndexes = [];\n      var mergedActiveValueCells = [];\n      var len = activeValueCells.length;\n      var pathKeys = getFullPathKeys(options, fieldNames);\n\n      // Fill validate active value cells and index\n      var _loop = function _loop(i) {\n        // Mark the active index for current options\n        var nextActiveIndex = currentOptions.findIndex(function (option, index) {\n          return (pathKeys[index] ? toPathKey(pathKeys[index]) : option[fieldNames.value]) === activeValueCells[i];\n        });\n        if (nextActiveIndex === -1) {\n          return 1; // break\n        }\n        activeIndex = nextActiveIndex;\n        mergedActiveIndexes.push(activeIndex);\n        mergedActiveValueCells.push(activeValueCells[i]);\n        currentOptions = currentOptions[activeIndex][fieldNames.children];\n      };\n      for (var i = 0; i < len && currentOptions; i += 1) {\n        if (_loop(i)) break;\n      }\n\n      // Fill last active options\n      var activeOptions = options;\n      for (var _i = 0; _i < mergedActiveIndexes.length - 1; _i += 1) {\n        activeOptions = activeOptions[mergedActiveIndexes[_i]][fieldNames.children];\n      }\n      return [mergedActiveValueCells, activeIndex, activeOptions, pathKeys];\n    }, [activeValueCells, fieldNames, options]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 4),\n    validActiveValueCells = _React$useMemo2[0],\n    lastActiveIndex = _React$useMemo2[1],\n    lastActiveOptions = _React$useMemo2[2],\n    fullPathKeys = _React$useMemo2[3];\n\n  // Update active value cells and scroll to target element\n  var internalSetActiveValueCells = function internalSetActiveValueCells(next) {\n    setActiveValueCells(next);\n  };\n\n  // Same options offset\n  var offsetActiveOption = function offsetActiveOption(offset) {\n    var len = lastActiveOptions.length;\n    var currentIndex = lastActiveIndex;\n    if (currentIndex === -1 && offset < 0) {\n      currentIndex = len;\n    }\n    for (var i = 0; i < len; i += 1) {\n      currentIndex = (currentIndex + offset + len) % len;\n      var _option = lastActiveOptions[currentIndex];\n      if (_option && !_option.disabled) {\n        var nextActiveCells = validActiveValueCells.slice(0, -1).concat(fullPathKeys[currentIndex] ? toPathKey(fullPathKeys[currentIndex]) : _option[fieldNames.value]);\n        internalSetActiveValueCells(nextActiveCells);\n        return;\n      }\n    }\n  };\n\n  // Different options offset\n  var prevColumn = function prevColumn() {\n    if (validActiveValueCells.length > 1) {\n      var nextActiveCells = validActiveValueCells.slice(0, -1);\n      internalSetActiveValueCells(nextActiveCells);\n    } else {\n      toggleOpen(false);\n    }\n  };\n  var nextColumn = function nextColumn() {\n    var _lastActiveOptions$la;\n    var nextOptions = ((_lastActiveOptions$la = lastActiveOptions[lastActiveIndex]) === null || _lastActiveOptions$la === void 0 ? void 0 : _lastActiveOptions$la[fieldNames.children]) || [];\n    var nextOption = nextOptions.find(function (option) {\n      return !option.disabled;\n    });\n    if (nextOption) {\n      var nextActiveCells = [].concat(_toConsumableArray(validActiveValueCells), [nextOption[fieldNames.value]]);\n      internalSetActiveValueCells(nextActiveCells);\n    }\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      // scrollTo: treeRef.current?.scrollTo,\n      onKeyDown: function onKeyDown(event) {\n        var which = event.which;\n        switch (which) {\n          // >>> Arrow keys\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n            {\n              var offset = 0;\n              if (which === KeyCode.UP) {\n                offset = -1;\n              } else if (which === KeyCode.DOWN) {\n                offset = 1;\n              }\n              if (offset !== 0) {\n                offsetActiveOption(offset);\n              }\n              break;\n            }\n          case KeyCode.LEFT:\n            {\n              if (searchValue) {\n                break;\n              }\n              if (rtl) {\n                nextColumn();\n              } else {\n                prevColumn();\n              }\n              break;\n            }\n          case KeyCode.RIGHT:\n            {\n              if (searchValue) {\n                break;\n              }\n              if (rtl) {\n                prevColumn();\n              } else {\n                nextColumn();\n              }\n              break;\n            }\n          case KeyCode.BACKSPACE:\n            {\n              if (!searchValue) {\n                prevColumn();\n              }\n              break;\n            }\n\n          // >>> Select\n          case KeyCode.ENTER:\n            {\n              if (validActiveValueCells.length) {\n                var _option2 = lastActiveOptions[lastActiveIndex];\n\n                // Search option should revert back of origin options\n                var originOptions = (_option2 === null || _option2 === void 0 ? void 0 : _option2[SEARCH_MARK]) || [];\n                if (originOptions.length) {\n                  onKeyBoardSelect(originOptions.map(function (opt) {\n                    return opt[fieldNames.value];\n                  }), originOptions[originOptions.length - 1]);\n                } else {\n                  onKeyBoardSelect(validActiveValueCells, lastActiveOptions[lastActiveIndex]);\n                }\n              }\n              break;\n            }\n\n          // >>> Close\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n              if (open) {\n                event.stopPropagation();\n              }\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {}\n    };\n  });\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}