/**
 * 数据分析页面
 * 展示健康数据的深度分析和可视化图表
 */

import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Select, DatePicker, Tabs, Statistic, Progress } from 'antd';
// 暂时不使用图表库，使用简单的数据展示
import {
  RiseOutlined,
  FallOutlined,
  HeartOutlined,
  TrophyOutlined,
  SmileOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';
import dayjs from 'dayjs';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

// 样式化组件
const PageTitle = styled.h1`
  color: white;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 24px;
  text-align: center;
`;

const StyledCard = styled(Card)`
  background: rgba(255, 255, 255, 0.25) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18) !important;
  border-radius: 20px !important;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37) !important;
  transition: all 0.3s ease !important;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5) !important;
  }
  
  .ant-card-head {
    background: transparent !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.18) !important;
    
    .ant-card-head-title {
      color: white !important;
      font-weight: 600 !important;
    }
  }
  
  .ant-card-body {
    background: transparent !important;
  }
`;

const ControlPanel = styled.div`
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 24px;
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
  
  .control-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    label {
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
      font-weight: 500;
    }
  }
`;

const StyledTabs = styled(Tabs)`
  .ant-tabs-nav {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 4px;
    margin-bottom: 24px;
    
    .ant-tabs-tab {
      background: transparent !important;
      border: none !important;
      color: rgba(255, 255, 255, 0.7) !important;
      border-radius: 8px !important;
      margin: 0 4px !important;
      
      &.ant-tabs-tab-active {
        background: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
      }
    }
    
    .ant-tabs-ink-bar {
      display: none !important;
    }
  }
`;

const MetricCard = styled(StyledCard)`
  text-align: center;
  
  .ant-statistic-title {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 14px !important;
    margin-bottom: 8px !important;
  }
  
  .ant-statistic-content {
    color: white !important;
    
    .ant-statistic-content-value {
      font-weight: 700 !important;
    }
  }
  
  .trend-indicator {
    margin-top: 8px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    
    &.positive {
      color: #34C759;
    }
    
    &.negative {
      color: #FF3B30;
    }
    
    &.neutral {
      color: rgba(255, 255, 255, 0.6);
    }
  }
`;

// 图表颜色配置
const COLORS = ['#007AFF', '#34C759', '#FF9500', '#FF3B30', '#AF52DE', '#5AC8FA'];

function Analytics() {
  const [selectedMetric, setSelectedMetric] = useState('health_score');
  const [dateRange, setDateRange] = useState([dayjs().subtract(30, 'day'), dayjs()]);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);

  // 模拟分析数据
  const mockAnalyticsData = {
    trends: {
      health_score: {
        labels: ['1月1日', '1月8日', '1月15日', '1月22日', '1月29日'],
        datasets: [{
          label: '健康评分',
          data: [75, 78, 82, 80, 85],
          borderColor: '#007AFF',
          backgroundColor: 'rgba(0, 122, 255, 0.1)',
          fill: true,
          tension: 0.4,
        }]
      },
      weight: {
        labels: ['1月1日', '1月8日', '1月15日', '1月22日', '1月29日'],
        datasets: [{
          label: '体重 (kg)',
          data: [70.5, 70.2, 69.8, 69.5, 69.2],
          borderColor: '#34C759',
          backgroundColor: 'rgba(52, 199, 89, 0.1)',
          fill: true,
          tension: 0.4,
        }]
      },
      heart_rate: {
        labels: ['1月1日', '1月8日', '1月15日', '1月22日', '1月29日'],
        datasets: [{
          label: '心率 (bpm)',
          data: [75, 73, 72, 71, 70],
          borderColor: '#FF3B30',
          backgroundColor: 'rgba(255, 59, 48, 0.1)',
          fill: true,
          tension: 0.4,
        }]
      }
    },
    exerciseStats: {
      labels: ['跑步', '游泳', '健身', '瑜伽'],
      datasets: [{
        data: [12, 8, 15, 6],
        backgroundColor: ['#007AFF', '#34C759', '#FF9500', '#AF52DE'],
        borderWidth: 0,
      }]
    },
    moodDistribution: {
      labels: ['很好', '好', '一般', '不好'],
      datasets: [{
        data: [8, 12, 6, 2],
        backgroundColor: ['#34C759', '#007AFF', '#FF9500', '#FF3B30'],
        borderWidth: 0,
      }]
    },
    weeklyActivity: {
      labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      datasets: [
        {
          label: '运动时长(分钟)',
          data: [45, 30, 60, 0, 40, 90, 75],
          backgroundColor: '#007AFF',
        },
        {
          label: '睡眠时长(小时)',
          data: [7.5, 8, 7, 6.5, 7.5, 9, 8.5],
          backgroundColor: '#34C759',
        },
        {
          label: '心情评分',
          data: [7, 8, 6, 5, 8, 9, 8],
          backgroundColor: '#FF9500',
        }
      ]
    },
    metrics: {
      avgHealthScore: 82,
      healthScoreTrend: 'up',
      avgWeight: 69.6,
      weightTrend: 'down',
      avgHeartRate: 72,
      heartRateTrend: 'down',
      totalExercises: 41,
      exerciseTrend: 'up',
    }
  };

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedMetric, dateRange]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800));
    setAnalyticsData(mockAnalyticsData);
    setLoading(false);
  };



  const renderTrendIndicator = (trend) => {
    if (trend === 'up') {
      return (
        <div className="trend-indicator positive">
          <RiseOutlined /> 上升趋势
        </div>
      );
    } else if (trend === 'down') {
      return (
        <div className="trend-indicator negative">
          <FallOutlined /> 下降趋势
        </div>
      );
    }
    return (
      <div className="trend-indicator neutral">
        稳定
      </div>
    );
  };

  if (loading || !analyticsData) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <div style={{ color: 'white', fontSize: '18px' }}>加载分析数据中...</div>
      </div>
    );
  }

  return (
    <div className="slide-up">
      <PageTitle>健康数据分析</PageTitle>
      
      {/* 控制面板 */}
      <ControlPanel>
        <div className="control-item">
          <label>分析指标</label>
          <Select
            value={selectedMetric}
            onChange={setSelectedMetric}
            style={{ width: 150 }}
          >
            <Option value="health_score">健康评分</Option>
            <Option value="weight">体重变化</Option>
            <Option value="heart_rate">心率趋势</Option>
          </Select>
        </div>
        
        <div className="control-item">
          <label>时间范围</label>
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            style={{ width: 250 }}
          />
        </div>
      </ControlPanel>

      {/* 关键指标卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <MetricCard>
            <Statistic
              title="平均健康评分"
              value={analyticsData.metrics.avgHealthScore}
              prefix={<HeartOutlined style={{ color: '#FF3B30' }} />}
            />
            {renderTrendIndicator(analyticsData.metrics.healthScoreTrend)}
          </MetricCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <MetricCard>
            <Statistic
              title="平均体重"
              value={analyticsData.metrics.avgWeight}
              precision={1}
              suffix="kg"
              prefix={<TrophyOutlined style={{ color: '#007AFF' }} />}
            />
            {renderTrendIndicator(analyticsData.metrics.weightTrend)}
          </MetricCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <MetricCard>
            <Statistic
              title="平均心率"
              value={analyticsData.metrics.avgHeartRate}
              suffix="bpm"
              prefix={<HeartOutlined style={{ color: '#34C759' }} />}
            />
            {renderTrendIndicator(analyticsData.metrics.heartRateTrend)}
          </MetricCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <MetricCard>
            <Statistic
              title="运动次数"
              value={analyticsData.metrics.totalExercises}
              suffix="次"
              prefix={<SmileOutlined style={{ color: '#FF9500' }} />}
            />
            {renderTrendIndicator(analyticsData.metrics.exerciseTrend)}
          </MetricCard>
        </Col>
      </Row>

      {/* 分析图表 */}
      <StyledTabs defaultActiveKey="trends">
        <TabPane tab="趋势分析" key="trends">
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={16}>
              <StyledCard title="健康指标趋势">
                <div style={{ padding: '20px', color: 'white' }}>
                  <div style={{ marginBottom: '16px', fontSize: '16px', fontWeight: '600' }}>
                    {selectedMetric === 'health_score' ? '健康评分趋势' :
                     selectedMetric === 'weight' ? '体重变化趋势' : '心率变化趋势'}
                  </div>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    {analyticsData.trends[selectedMetric].labels.map((label, index) => (
                      <div key={index} style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        padding: '8px 12px',
                        background: 'rgba(255, 255, 255, 0.1)',
                        borderRadius: '8px'
                      }}>
                        <span>{label}</span>
                        <span style={{ fontWeight: '600' }}>
                          {analyticsData.trends[selectedMetric].datasets[0].data[index]}
                          {selectedMetric === 'weight' ? ' kg' : selectedMetric === 'heart_rate' ? ' bpm' : ''}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </StyledCard>
            </Col>
            
            <Col xs={24} lg={8}>
              <StyledCard title="运动类型分布">
                <div style={{ padding: '20px', color: 'white' }}>
                  {analyticsData.exerciseStats.labels.map((label, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '12px 0',
                      borderBottom: index < analyticsData.exerciseStats.labels.length - 1 ?
                        '1px solid rgba(255, 255, 255, 0.1)' : 'none'
                    }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <div style={{
                          width: '12px',
                          height: '12px',
                          borderRadius: '50%',
                          backgroundColor: analyticsData.exerciseStats.datasets[0].backgroundColor[index]
                        }}></div>
                        <span>{label}</span>
                      </div>
                      <span style={{ fontWeight: '600' }}>
                        {analyticsData.exerciseStats.datasets[0].data[index]} 次
                      </span>
                    </div>
                  ))}
                </div>
              </StyledCard>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="活动分析" key="activity">
          <Row gutter={[16, 16]}>
            <Col xs={24}>
              <StyledCard title="每周活动统计">
                <div style={{ padding: '20px', color: 'white' }}>
                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: '8px', marginBottom: '20px' }}>
                    {analyticsData.weeklyActivity.labels.map((day, index) => (
                      <div key={index} style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '12px', marginBottom: '8px', opacity: 0.8 }}>{day}</div>
                        <div style={{
                          background: 'rgba(0, 122, 255, 0.2)',
                          height: `${analyticsData.weeklyActivity.datasets[0].data[index] * 2}px`,
                          minHeight: '20px',
                          borderRadius: '4px',
                          marginBottom: '4px',
                          display: 'flex',
                          alignItems: 'end',
                          justifyContent: 'center',
                          fontSize: '10px',
                          color: 'white'
                        }}>
                          {analyticsData.weeklyActivity.datasets[0].data[index]}
                        </div>
                        <div style={{ fontSize: '10px', opacity: 0.6 }}>运动</div>
                      </div>
                    ))}
                  </div>
                  <div style={{ fontSize: '14px', opacity: 0.8 }}>
                    <div>平均运动时长: {(analyticsData.weeklyActivity.datasets[0].data.reduce((a, b) => a + b, 0) / 7).toFixed(1)} 分钟/天</div>
                    <div>平均睡眠时长: {(analyticsData.weeklyActivity.datasets[1].data.reduce((a, b) => a + b, 0) / 7).toFixed(1)} 小时/天</div>
                    <div>平均心情评分: {(analyticsData.weeklyActivity.datasets[2].data.reduce((a, b) => a + b, 0) / 7).toFixed(1)} 分</div>
                  </div>
                </div>
              </StyledCard>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="心情分析" key="mood">
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <StyledCard title="心情分布">
                <div style={{ padding: '20px', color: 'white' }}>
                  {analyticsData.moodDistribution.labels.map((mood, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '12px 0',
                      borderBottom: index < analyticsData.moodDistribution.labels.length - 1 ?
                        '1px solid rgba(255, 255, 255, 0.1)' : 'none'
                    }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <div style={{
                          width: '12px',
                          height: '12px',
                          borderRadius: '50%',
                          backgroundColor: analyticsData.moodDistribution.datasets[0].backgroundColor[index]
                        }}></div>
                        <span>{mood}</span>
                      </div>
                      <span style={{ fontWeight: '600' }}>
                        {analyticsData.moodDistribution.datasets[0].data[index]} 天
                      </span>
                    </div>
                  ))}
                </div>
              </StyledCard>
            </Col>
            
            <Col xs={24} lg={12}>
              <StyledCard title="心情统计">
                <div style={{ padding: '20px 0' }}>
                  {analyticsData.moodDistribution.map((item, index) => (
                    <div key={index} style={{ marginBottom: '16px' }}>
                      <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        marginBottom: '8px',
                        color: 'white'
                      }}>
                        <span>{item.mood}</span>
                        <span>{item.count} 天</span>
                      </div>
                      <Progress 
                        percent={(item.count / 28) * 100} 
                        strokeColor={item.color}
                        showInfo={false}
                      />
                    </div>
                  ))}
                </div>
              </StyledCard>
            </Col>
          </Row>
        </TabPane>
      </StyledTabs>
    </div>
  );
}

export default Analytics;
