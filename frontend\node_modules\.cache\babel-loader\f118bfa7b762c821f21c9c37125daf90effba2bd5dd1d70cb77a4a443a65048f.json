{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genSelectionStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    iconCls,\n    fontSizeIcon,\n    padding,\n    paddingXS,\n    headerIconColor,\n    headerIconHoverColor,\n    tableSelectionColumnWidth,\n    tableSelectedRowBg,\n    tableSelectedRowHoverBg,\n    tableRowHoverBg,\n    tablePaddingHorizontal,\n    calc\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Selections ==========================\n      [`${componentCls}-selection-col`]: {\n        width: tableSelectionColumnWidth,\n        [`&${componentCls}-selection-col-with-dropdown`]: {\n          width: calc(tableSelectionColumnWidth).add(fontSizeIcon).add(calc(padding).div(4)).equal()\n        }\n      },\n      [`${componentCls}-bordered ${componentCls}-selection-col`]: {\n        width: calc(tableSelectionColumnWidth).add(calc(paddingXS).mul(2)).equal(),\n        [`&${componentCls}-selection-col-with-dropdown`]: {\n          width: calc(tableSelectionColumnWidth).add(fontSizeIcon).add(calc(padding).div(4)).add(calc(paddingXS).mul(2)).equal()\n        }\n      },\n      [`\n        table tr th${componentCls}-selection-column,\n        table tr td${componentCls}-selection-column,\n        ${componentCls}-selection-column\n      `]: {\n        paddingInlineEnd: token.paddingXS,\n        paddingInlineStart: token.paddingXS,\n        textAlign: 'center',\n        [`${antCls}-radio-wrapper`]: {\n          marginInlineEnd: 0\n        }\n      },\n      [`table tr th${componentCls}-selection-column${componentCls}-cell-fix-left`]: {\n        zIndex: calc(token.zIndexTableFixed).add(1).equal({\n          unit: false\n        })\n      },\n      [`table tr th${componentCls}-selection-column::after`]: {\n        backgroundColor: 'transparent !important'\n      },\n      [`${componentCls}-selection`]: {\n        position: 'relative',\n        display: 'inline-flex',\n        flexDirection: 'column'\n      },\n      [`${componentCls}-selection-extra`]: {\n        position: 'absolute',\n        top: 0,\n        zIndex: 1,\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationSlow}`,\n        marginInlineStart: '100%',\n        paddingInlineStart: unit(calc(tablePaddingHorizontal).div(4).equal()),\n        [iconCls]: {\n          color: headerIconColor,\n          fontSize: fontSizeIcon,\n          verticalAlign: 'baseline',\n          '&:hover': {\n            color: headerIconHoverColor\n          }\n        }\n      },\n      // ============================= Rows =============================\n      [`${componentCls}-tbody`]: {\n        [`${componentCls}-row`]: {\n          [`&${componentCls}-row-selected`]: {\n            [`> ${componentCls}-cell`]: {\n              background: tableSelectedRowBg,\n              '&-row-hover': {\n                background: tableSelectedRowHoverBg\n              }\n            }\n          },\n          [`> ${componentCls}-cell-row-hover`]: {\n            background: tableRowHoverBg\n          }\n        }\n      }\n    }\n  };\n};\nexport default genSelectionStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}