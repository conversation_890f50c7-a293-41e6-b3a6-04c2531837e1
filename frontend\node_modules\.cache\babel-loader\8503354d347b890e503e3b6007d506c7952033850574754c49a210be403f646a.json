{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { warning } from 'rc-util';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport * as React from 'react';\nimport { formatValue } from \"../../../utils/dateUtil\";\nexport default function useInputProps(props, /** Used for SinglePicker */\npostProps) {\n  var format = props.format,\n    maskFormat = props.maskFormat,\n    generateConfig = props.generateConfig,\n    locale = props.locale,\n    preserveInvalidOnBlur = props.preserveInvalidOnBlur,\n    inputReadOnly = props.inputReadOnly,\n    required = props.required,\n    ariaRequired = props['aria-required'],\n    onSubmit = props.onSubmit,\n    _onFocus = props.onFocus,\n    _onBlur = props.onBlur,\n    onInputChange = props.onInputChange,\n    onInvalid = props.onInvalid,\n    open = props.open,\n    onOpenChange = props.onOpenChange,\n    _onKeyDown = props.onKeyDown,\n    _onChange = props.onChange,\n    activeHelp = props.activeHelp,\n    name = props.name,\n    autoComplete = props.autoComplete,\n    id = props.id,\n    value = props.value,\n    invalid = props.invalid,\n    placeholder = props.placeholder,\n    disabled = props.disabled,\n    activeIndex = props.activeIndex,\n    allHelp = props.allHelp,\n    picker = props.picker;\n\n  // ======================== Parser ========================\n  var parseDate = function parseDate(str, formatStr) {\n    var parsed = generateConfig.locale.parse(locale.locale, str, [formatStr]);\n    return parsed && generateConfig.isValidate(parsed) ? parsed : null;\n  };\n\n  // ========================= Text =========================\n  var firstFormat = format[0];\n  var getText = React.useCallback(function (date) {\n    return formatValue(date, {\n      locale: locale,\n      format: firstFormat,\n      generateConfig: generateConfig\n    });\n  }, [locale, generateConfig, firstFormat]);\n  var valueTexts = React.useMemo(function () {\n    return value.map(getText);\n  }, [value, getText]);\n\n  // ========================= Size =========================\n  var size = React.useMemo(function () {\n    var defaultSize = picker === 'time' ? 8 : 10;\n    var length = typeof firstFormat === 'function' ? firstFormat(generateConfig.getNow()).length : firstFormat.length;\n    return Math.max(defaultSize, length) + 2;\n  }, [firstFormat, picker, generateConfig]);\n\n  // ======================= Validate =======================\n  var _validateFormat = function validateFormat(text) {\n    for (var i = 0; i < format.length; i += 1) {\n      var singleFormat = format[i];\n\n      // Only support string type\n      if (typeof singleFormat === 'string') {\n        var parsed = parseDate(text, singleFormat);\n        if (parsed) {\n          return parsed;\n        }\n      }\n    }\n    return false;\n  };\n\n  // ======================== Input =========================\n  var getInputProps = function getInputProps(index) {\n    function getProp(propValue) {\n      return index !== undefined ? propValue[index] : propValue;\n    }\n    var pickedAttrs = pickAttrs(props, {\n      aria: true,\n      data: true\n    });\n    var inputProps = _objectSpread(_objectSpread({}, pickedAttrs), {}, {\n      // ============== Shared ==============\n      format: maskFormat,\n      validateFormat: function validateFormat(text) {\n        return !!_validateFormat(text);\n      },\n      preserveInvalidOnBlur: preserveInvalidOnBlur,\n      readOnly: inputReadOnly,\n      required: required,\n      'aria-required': ariaRequired,\n      name: name,\n      autoComplete: autoComplete,\n      size: size,\n      // ============= By Index =============\n      id: getProp(id),\n      value: getProp(valueTexts) || '',\n      invalid: getProp(invalid),\n      placeholder: getProp(placeholder),\n      active: activeIndex === index,\n      helped: allHelp || activeHelp && activeIndex === index,\n      disabled: getProp(disabled),\n      onFocus: function onFocus(event) {\n        _onFocus(event, index);\n      },\n      onBlur: function onBlur(event) {\n        // Blur do not trigger close\n        // Since it may focus to the popup panel\n        _onBlur(event, index);\n      },\n      onSubmit: onSubmit,\n      // Get validate text value\n      onChange: function onChange(text) {\n        onInputChange();\n        var parsed = _validateFormat(text);\n        if (parsed) {\n          onInvalid(false, index);\n          _onChange(parsed, index);\n          return;\n        }\n\n        // Tell outer that the value typed is invalid.\n        // If text is empty, it means valid.\n        onInvalid(!!text, index);\n      },\n      onHelp: function onHelp() {\n        onOpenChange(true, {\n          index: index\n        });\n      },\n      onKeyDown: function onKeyDown(event) {\n        var prevented = false;\n        _onKeyDown === null || _onKeyDown === void 0 || _onKeyDown(event, function () {\n          if (process.env.NODE_ENV !== 'production') {\n            warning(false, '`preventDefault` callback is deprecated. Please call `event.preventDefault` directly.');\n          }\n          prevented = true;\n        });\n        if (!event.defaultPrevented && !prevented) {\n          switch (event.key) {\n            case 'Escape':\n              onOpenChange(false, {\n                index: index\n              });\n              break;\n            case 'Enter':\n              if (!open) {\n                onOpenChange(true);\n              }\n              break;\n          }\n        }\n      }\n    }, postProps === null || postProps === void 0 ? void 0 : postProps({\n      valueTexts: valueTexts\n    }));\n\n    // ============== Clean Up ==============\n    Object.keys(inputProps).forEach(function (key) {\n      if (inputProps[key] === undefined) {\n        delete inputProps[key];\n      }\n    });\n    return inputProps;\n  };\n  return [getInputProps, getText];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}