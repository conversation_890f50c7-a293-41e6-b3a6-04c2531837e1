{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genSizeStyle = token => {\n  const {\n    componentCls,\n    tableExpandColumnWidth,\n    calc\n  } = token;\n  const getSizeStyle = (size, paddingVertical, paddingHorizontal, fontSize) => ({\n    [`${componentCls}${componentCls}-${size}`]: {\n      fontSize,\n      [`\n        ${componentCls}-title,\n        ${componentCls}-footer,\n        ${componentCls}-cell,\n        ${componentCls}-thead > tr > th,\n        ${componentCls}-tbody > tr > th,\n        ${componentCls}-tbody > tr > td,\n        tfoot > tr > th,\n        tfoot > tr > td\n      `]: {\n        padding: `${unit(paddingVertical)} ${unit(paddingHorizontal)}`\n      },\n      [`${componentCls}-filter-trigger`]: {\n        marginInlineEnd: unit(calc(paddingHorizontal).div(2).mul(-1).equal())\n      },\n      [`${componentCls}-expanded-row-fixed`]: {\n        margin: `${unit(calc(paddingVertical).mul(-1).equal())} ${unit(calc(paddingHorizontal).mul(-1).equal())}`\n      },\n      [`${componentCls}-tbody`]: {\n        // ========================= Nest Table ===========================\n        [`${componentCls}-wrapper:only-child ${componentCls}`]: {\n          marginBlock: unit(calc(paddingVertical).mul(-1).equal()),\n          marginInline: `${unit(calc(tableExpandColumnWidth).sub(paddingHorizontal).equal())} ${unit(calc(paddingHorizontal).mul(-1).equal())}`\n        }\n      },\n      // https://github.com/ant-design/ant-design/issues/35167\n      [`${componentCls}-selection-extra`]: {\n        paddingInlineStart: unit(calc(paddingHorizontal).div(4).equal())\n      }\n    }\n  });\n  return {\n    [`${componentCls}-wrapper`]: Object.assign(Object.assign({}, getSizeStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle, token.tableFontSizeMiddle)), getSizeStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall, token.tableFontSizeSmall))\n  };\n};\nexport default genSizeStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}