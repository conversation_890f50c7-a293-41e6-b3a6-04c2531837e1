{"ast": null, "code": "import { useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport PerfContext from \"../context/PerfContext\";\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport useFlattenRecords from \"../hooks/useFlattenRecords\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport { getColumnsKey } from \"../utils/valueUtil\";\nimport BodyRow from \"./BodyRow\";\nimport ExpandedRow from \"./ExpandedRow\";\nimport MeasureRow from \"./MeasureRow\";\nfunction Body(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var data = props.data,\n    measureColumnWidth = props.measureColumnWidth;\n  var _useContext = useContext(TableContext, ['prefixCls', 'getComponent', 'onColumnResize', 'flattenColumns', 'getRowKey', 'expandedKeys', 'childrenColumnName', 'emptyNode', 'expandedRowOffset', 'fixedInfoList', 'colWidths']),\n    prefixCls = _useContext.prefixCls,\n    getComponent = _useContext.getComponent,\n    onColumnResize = _useContext.onColumnResize,\n    flattenColumns = _useContext.flattenColumns,\n    getRowKey = _useContext.getRowKey,\n    expandedKeys = _useContext.expandedKeys,\n    childrenColumnName = _useContext.childrenColumnName,\n    emptyNode = _useContext.emptyNode,\n    _useContext$expandedR = _useContext.expandedRowOffset,\n    expandedRowOffset = _useContext$expandedR === void 0 ? 0 : _useContext$expandedR,\n    colWidths = _useContext.colWidths;\n  var flattenData = useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey);\n  var rowKeys = React.useMemo(function () {\n    return flattenData.map(function (item) {\n      return item.rowKey;\n    });\n  }, [flattenData]);\n\n  // =================== Performance ====================\n  var perfRef = React.useRef({\n    renderWithProps: false\n  });\n\n  // ===================== Expanded =====================\n  // `expandedRowOffset` data is same for all the rows.\n  // Let's calc on Body side to save performance.\n  var expandedRowInfo = React.useMemo(function () {\n    var expandedColSpan = flattenColumns.length - expandedRowOffset;\n    var expandedStickyStart = 0;\n    for (var i = 0; i < expandedRowOffset; i += 1) {\n      expandedStickyStart += colWidths[i] || 0;\n    }\n    return {\n      offset: expandedRowOffset,\n      colSpan: expandedColSpan,\n      sticky: expandedStickyStart\n    };\n  }, [flattenColumns.length, expandedRowOffset, colWidths]);\n\n  // ====================== Render ======================\n  var WrapperComponent = getComponent(['body', 'wrapper'], 'tbody');\n  var trComponent = getComponent(['body', 'row'], 'tr');\n  var tdComponent = getComponent(['body', 'cell'], 'td');\n  var thComponent = getComponent(['body', 'cell'], 'th');\n  var rows;\n  if (data.length) {\n    rows = flattenData.map(function (item, idx) {\n      var record = item.record,\n        indent = item.indent,\n        renderIndex = item.index,\n        rowKey = item.rowKey;\n      return /*#__PURE__*/React.createElement(BodyRow, {\n        key: rowKey,\n        rowKey: rowKey,\n        rowKeys: rowKeys,\n        record: record,\n        index: idx,\n        renderIndex: renderIndex,\n        rowComponent: trComponent,\n        cellComponent: tdComponent,\n        scopeCellComponent: thComponent,\n        indent: indent\n        // Expanded row info\n        ,\n\n        expandedRowInfo: expandedRowInfo\n      });\n    });\n  } else {\n    rows = /*#__PURE__*/React.createElement(ExpandedRow, {\n      expanded: true,\n      className: \"\".concat(prefixCls, \"-placeholder\"),\n      prefixCls: prefixCls,\n      component: trComponent,\n      cellComponent: tdComponent,\n      colSpan: flattenColumns.length,\n      isEmpty: true\n    }, emptyNode);\n  }\n  var columnsKey = getColumnsKey(flattenColumns);\n  return /*#__PURE__*/React.createElement(PerfContext.Provider, {\n    value: perfRef.current\n  }, /*#__PURE__*/React.createElement(WrapperComponent, {\n    className: \"\".concat(prefixCls, \"-tbody\")\n  }, measureColumnWidth && /*#__PURE__*/React.createElement(MeasureRow, {\n    prefixCls: prefixCls,\n    columnsKey: columnsKey,\n    onColumnResize: onColumnResize\n  }), rows));\n}\nif (process.env.NODE_ENV !== 'production') {\n  Body.displayName = 'Body';\n}\nexport default responseImmutable(Body);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}