{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"id\", \"prefixCls\", \"steps\", \"strokeWidth\", \"trailWidth\", \"gapDegree\", \"gapPosition\", \"trailColor\", \"strokeLinecap\", \"style\", \"className\", \"strokeColor\", \"percent\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { defaultProps, useTransitionDuration } from \"../common\";\nimport useId from \"../hooks/useId\";\nimport PtgCircle from \"./PtgCircle\";\nimport { VIEW_BOX_SIZE, getCircleStyle } from \"./util\";\nfunction toArray(value) {\n  var mergedValue = value !== null && value !== void 0 ? value : [];\n  return Array.isArray(mergedValue) ? mergedValue : [mergedValue];\n}\nvar Circle = function Circle(props) {\n  var _defaultProps$props = _objectSpread(_objectSpread({}, defaultProps), props),\n    id = _defaultProps$props.id,\n    prefixCls = _defaultProps$props.prefixCls,\n    steps = _defaultProps$props.steps,\n    strokeWidth = _defaultProps$props.strokeWidth,\n    trailWidth = _defaultProps$props.trailWidth,\n    _defaultProps$props$g = _defaultProps$props.gapDegree,\n    gapDegree = _defaultProps$props$g === void 0 ? 0 : _defaultProps$props$g,\n    gapPosition = _defaultProps$props.gapPosition,\n    trailColor = _defaultProps$props.trailColor,\n    strokeLinecap = _defaultProps$props.strokeLinecap,\n    style = _defaultProps$props.style,\n    className = _defaultProps$props.className,\n    strokeColor = _defaultProps$props.strokeColor,\n    percent = _defaultProps$props.percent,\n    restProps = _objectWithoutProperties(_defaultProps$props, _excluded);\n  var halfSize = VIEW_BOX_SIZE / 2;\n  var mergedId = useId(id);\n  var gradientId = \"\".concat(mergedId, \"-gradient\");\n  var radius = halfSize - strokeWidth / 2;\n  var perimeter = Math.PI * 2 * radius;\n  var rotateDeg = gapDegree > 0 ? 90 + gapDegree / 2 : -90;\n  var perimeterWithoutGap = perimeter * ((360 - gapDegree) / 360);\n  var _ref = _typeof(steps) === 'object' ? steps : {\n      count: steps,\n      gap: 2\n    },\n    stepCount = _ref.count,\n    stepGap = _ref.gap;\n  var percentList = toArray(percent);\n  var strokeColorList = toArray(strokeColor);\n  var gradient = strokeColorList.find(function (color) {\n    return color && _typeof(color) === 'object';\n  });\n  var isConicGradient = gradient && _typeof(gradient) === 'object';\n  var mergedStrokeLinecap = isConicGradient ? 'butt' : strokeLinecap;\n  var circleStyle = getCircleStyle(perimeter, perimeterWithoutGap, 0, 100, rotateDeg, gapDegree, gapPosition, trailColor, mergedStrokeLinecap, strokeWidth);\n  var paths = useTransitionDuration();\n  var getStokeList = function getStokeList() {\n    var stackPtg = 0;\n    return percentList.map(function (ptg, index) {\n      var color = strokeColorList[index] || strokeColorList[strokeColorList.length - 1];\n      var circleStyleForStack = getCircleStyle(perimeter, perimeterWithoutGap, stackPtg, ptg, rotateDeg, gapDegree, gapPosition, color, mergedStrokeLinecap, strokeWidth);\n      stackPtg += ptg;\n      return /*#__PURE__*/React.createElement(PtgCircle, {\n        key: index,\n        color: color,\n        ptg: ptg,\n        radius: radius,\n        prefixCls: prefixCls,\n        gradientId: gradientId,\n        style: circleStyleForStack,\n        strokeLinecap: mergedStrokeLinecap,\n        strokeWidth: strokeWidth,\n        gapDegree: gapDegree,\n        ref: function ref(elem) {\n          // https://reactjs.org/docs/refs-and-the-dom.html#callback-refs\n          // React will call the ref callback with the DOM element when the component mounts,\n          // and call it with `null` when it unmounts.\n          // Refs are guaranteed to be up-to-date before componentDidMount or componentDidUpdate fires.\n\n          paths[index] = elem;\n        },\n        size: VIEW_BOX_SIZE\n      });\n    }).reverse();\n  };\n  var getStepStokeList = function getStepStokeList() {\n    // only show the first percent when pass steps\n    var current = Math.round(stepCount * (percentList[0] / 100));\n    var stepPtg = 100 / stepCount;\n    var stackPtg = 0;\n    return new Array(stepCount).fill(null).map(function (_, index) {\n      var color = index <= current - 1 ? strokeColorList[0] : trailColor;\n      var stroke = color && _typeof(color) === 'object' ? \"url(#\".concat(gradientId, \")\") : undefined;\n      var circleStyleForStack = getCircleStyle(perimeter, perimeterWithoutGap, stackPtg, stepPtg, rotateDeg, gapDegree, gapPosition, color, 'butt', strokeWidth, stepGap);\n      stackPtg += (perimeterWithoutGap - circleStyleForStack.strokeDashoffset + stepGap) * 100 / perimeterWithoutGap;\n      return /*#__PURE__*/React.createElement(\"circle\", {\n        key: index,\n        className: \"\".concat(prefixCls, \"-circle-path\"),\n        r: radius,\n        cx: halfSize,\n        cy: halfSize,\n        stroke: stroke,\n        strokeWidth: strokeWidth,\n        opacity: 1,\n        style: circleStyleForStack,\n        ref: function ref(elem) {\n          paths[index] = elem;\n        }\n      });\n    });\n  };\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    className: classNames(\"\".concat(prefixCls, \"-circle\"), className),\n    viewBox: \"0 0 \".concat(VIEW_BOX_SIZE, \" \").concat(VIEW_BOX_SIZE),\n    style: style,\n    id: id,\n    role: \"presentation\"\n  }, restProps), !stepCount && /*#__PURE__*/React.createElement(\"circle\", {\n    className: \"\".concat(prefixCls, \"-circle-trail\"),\n    r: radius,\n    cx: halfSize,\n    cy: halfSize,\n    stroke: trailColor,\n    strokeLinecap: mergedStrokeLinecap,\n    strokeWidth: trailWidth || strokeWidth,\n    style: circleStyle\n  }), stepCount ? getStepStokeList() : getStokeList());\n};\nif (process.env.NODE_ENV !== 'production') {\n  Circle.displayName = 'Circle';\n}\nexport default Circle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}